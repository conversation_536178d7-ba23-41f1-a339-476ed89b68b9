{"logs": [{"outputFile": "com.example.NovaSchool.app-mergeNovaDebugResources-52:/values-v24/values-v24.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\929ae1d3a00f8bfd8a9a1aa81922ea47\\transformed\\media-1.1.0\\res\\values-v24\\values-v24.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,121,182,248", "endColumns": "65,60,65,66", "endOffsets": "116,177,243,310"}, "to": {"startLines": "4,5,6,7", "startColumns": "4,4,4,4", "startOffsets": "347,413,474,540", "endColumns": "65,60,65,66", "endOffsets": "408,469,535,602"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\57c1c0bacecfc631670f89f9a212e268\\transformed\\appcompat-1.6.1\\res\\values-v24\\values-v24.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,212", "endColumns": "156,134", "endOffsets": "207,342"}}]}]}