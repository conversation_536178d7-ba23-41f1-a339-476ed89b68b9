baseURL='https://jjr.ecocode.ovh/api'
keycloakUri='https://jjr.ecocode.ovh/realms/Eco-Code'
tokenUrl='https://jjr.ecocode.ovh/realms/Eco-Code/protocol/openid-connect/token'
logoutUrl='https://jjr.ecocode.ovh/realms/Eco-Code/protocol/openid-connect/logout'
introspectUrl='https://jjr.ecocode.ovh/realms/Eco-Code/protocol/openid-connect/token/introspect'
forgetPasswordURL='https://jjr.ecocode.ovh'
clientId='ishrakschool-back'
clientSecret='cttRhoDIkNiFzqJc9jh0w3OsZGKswQ6t'
ipAddress='jjr.ecocode.ovh'
facebookUrl='https://www.facebook.com/p/<PERSON><PERSON><PERSON>-100060608566436/'
nameFacebook='<PERSON><PERSON><PERSON>-<PERSON>'
instagramUrl='https://www.instagram.com/jeanjacques.gabes/'
nameInstagram='jeanjacques.gabes'
adresse1='8 Rue El Dorra, El Mnara, Teboulbou , 6032 Gabès'
adresse2=''
tel1='53277015'
tel2=''
tel3=''
tel4=''
mail='<EMAIL>'
youtubeUrl=''
schoolLogo='assets/logos/jjr.png'
name='jjr'
dashURL='https://jjr.ecocode.ovh/profile-eleve-parent'
backendLoginUrl='https://jjr.ecocode.ovh/api/users/login'
backendLogoutUrl='https://jjr.ecocode.ovh/api/users/logout'
confirmed=true
confirmedParent=true
niveaux='Préparatoire,1ère-Année,2ème-Année,3ème-Année,4ème-Année,5ème-Année,6ème-Année'
APP_NAME=''
CARNET_NAME= 'Carnet'