// import 'dart:io';

// import 'package:flutter/foundation.dart';
// import 'package:flutter/material.dart';
// import 'package:NovaSchool/commun/parent_app/app_PnavBar.dart';
// import 'package:NovaSchool/features/teacher_app/Home/home_Tscreen.dart';
// import 'package:logger/logger.dart';
// import '../../commun/teacher_app/app_TnavBar.dart';
// import '../admin/home/<USER>';
// import '../../utils/constants/colors.dart';
// import '../../utils/constants/images.dart';
// import '../../utils/constants/text.dart';
// import '../login/login_screen.dart';
// import 'package:openid_client/openid_client.dart';
// import '../../openid_io.dart'
//     if (dart.library.html) '../../openid_browser.dart';
// import 'package:shared_preferences/shared_preferences.dart';
// import 'package:jose/jose.dart';

// final log = Logger();
// const keycloakUri = 'http://....:9001/realms/Prestacode'; //keycloakUri from base url
// const scopes = ['openid', 'profile', 'email'];
// const adminRole = 'admin';
// const parentRole = 'parent';
// const enseignantRole = 'enseignant';
// const agentRole = 'agent';
// Credential? credential;
// late final Client client;

// Future<Client> getClient() async {
//   var uri = Uri.parse(keycloakUri);
//   if (!kIsWeb && Platform.isAndroid) uri = uri.replace(host: '********');
//   var clientId = 'ishrakschool-back';  //base Url

//   var issuer = await Issuer.discover(uri);
//   return Client(issuer, clientId);
// }

// class WelcomeContenu extends StatelessWidget {
//   const WelcomeContenu({
//     Key? key,
//   }) : super(key: key);

//   @override
//   Widget build(BuildContext context) {
//     return Center(
//       child: Column(
//         mainAxisAlignment: MainAxisAlignment.center,
//         children: [
//           /// Logo de l'école
//           const Image(
//             image: AssetImage(AppImages.schoolLogo),
//             width: 280,
//           ),
//           Padding(
//             padding: const EdgeInsets.all(40.0),
//             child: SingleChildScrollView(
//               physics: const AlwaysScrollableScrollPhysics(),
//               child: GestureDetector(
//                 // onTap: () {
//                 //   /// Ouvre une boîte de dialogue pour la connexion
//                 //   showGeneralDialog(
//                 //     barrierDismissible: true, /// Permet de fermer la boîte de dialogue en cliquant en dehors d'elle
//                 //     barrierLabel: 'Commencer',
//                 //     context: context,
//                 //     pageBuilder: (context, _, __) => Center(
//                 //       child: LoginScreen(), /// Affiche l'écran de connexion
//                 //     ),
//                 //   );
//                 // },
//                 onTap: () async {
//                   // Initialize the client if not already initialized
//                   client = await getClient();
//                   log.i('client: ${client}');
//                   try {
//                     log.i('client: ');
//                     // Authenticate and get credential
//                     var credential = await authenticate(client, scopes: scopes);

//                     // Log the access token
//                     log.i('Access Token: ${credential.accessToken}');

//                     // Extract roles from token
//                     var roles = extractRolesFromToken(
//                         credential.idToken.toCompactSerialization());

//                     // Store token and roles in local storage
//                     await storeTokenAndRoles(
//                         credential.idToken.toCompactSerialization(), roles);

//                     // Navigate based on roles
//                     navigateBasedOnRoles(context, roles);
//                   } catch (e, stackTrace) {
//                     log.e('Error during authentication: $e');
//                     ScaffoldMessenger.of(context).showSnackBar(
//                       SnackBar(
//                         content: Text('Error during authentication: $e'),
//                         duration: const Duration(seconds: 5),
//                       ),
//                     );
//                   }
//                 },

//                 /// Bouton de connexion
//                 /// Login Button
//                 child: Container(
//                   height: 50,
//                   decoration: const BoxDecoration(
//                     color: Colors.blue,
//                     borderRadius: BorderRadius.all(Radius.circular(30)),
//                   ),
//                   child: const Center(
//                     child: Text(
//                       'Login',
//                       style: TextStyle(
//                         fontSize: 18,
//                         fontWeight: FontWeight.w500,
//                         color: Colors.white,
//                       ),
//                     ),
//                   ),
//                 ),
//               ),
//             ),
//           ),
//         ],
//       ),
//     );
//   }

//   List<String> extractRolesFromToken(String accessToken) {
//     log.i('Access Token: $accessToken'); // Log the access token string

//     final jwt = JsonWebToken.unverified(accessToken);
//     final claims = jwt.claims.toJson();

//     log.i('Claims: $claims'); // Log the entire claims including resource_access

//     final resourceAccess = claims['resource_access'] as Map<String, dynamic>?;

//     final roles = <String>[];
//     if (resourceAccess != null) {
//       final clientRoles =
//           resourceAccess['ishrakschool-back']?['roles'] as List<dynamic>?;
//       if (clientRoles != null) {
//         roles.addAll(clientRoles.map((role) => role.toString()));
//       }
//     }

//     return roles;
//   }

//   Future<void> storeTokenAndRoles(String token, List<String> roles) async {
//     SharedPreferences prefs = await SharedPreferences.getInstance();
//     await prefs.setString('token', token);
//     await prefs.setStringList('roles', roles);
//   }

//   void navigateBasedOnRoles(BuildContext context, List<String> roles) {
//     if (roles.contains(adminRole)) {
//       Navigator.pushReplacement(
//         context,
//         MaterialPageRoute(builder: (context) => AHomeScreen()),
//       );
//     } else if (roles.contains(enseignantRole)) {
//       Navigator.pushReplacement(
//         context,
//         MaterialPageRoute(builder: (context) => THomeScreen()),
//       );
//     }else if (roles.contains(parentRole)) {
//       Navigator.pushReplacement(
//         context,
//         MaterialPageRoute(builder: (context) => ParentAppNavBar()),
//       );
//     }
//      else {
//       Navigator.pushReplacement(
//         context,
//         MaterialPageRoute(builder: (context) => AHomeScreen()),
//       );
//     }
//   }
// }

import 'package:NovaSchool/commun/admin_app/admin_navbar.dart';
import 'package:flutter/material.dart';
import 'package:NovaSchool/commun/base_url.dart';
import 'package:NovaSchool/commun/parent_app/app_PnavBar.dart';
import 'package:NovaSchool/features/admin/home/<USER>';
import 'package:NovaSchool/features/login/login_services.dart';
import 'package:NovaSchool/features/teacher_app/Home/home_Tscreen.dart';
import 'package:NovaSchool/services/auth_service.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import '../../utils/constants/colors.dart';
import '../../utils/constants/images.dart';
import '../../utils/constants/text.dart';
import '../../utils/constants/size.dart';
import '../../utils/device/devices_utility.dart';
import '../../utils/widgets/responsive_layout.dart';
import '../login/login_screen.dart';
import 'package:provider/provider.dart';

class WelcomeContenu extends StatelessWidget {
  const WelcomeContenu({
    Key? key,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final AuthService loginService = AuthService();

    return ResponsiveContainer(
      child: ResponsiveLayout(
        mobile: _buildMobileLayout(context, loginService),
        tablet: _buildTabletLayout(context, loginService),
        desktop: _buildDesktopLayout(context, loginService),
      ),
    );
  }

  Widget _buildMobileLayout(BuildContext context, AuthService loginService) {
    return Center(
      child: SingleChildScrollView(
        padding: EdgeInsets.all(AppSize.defaultSpace(context)),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            _buildLogo(context),
            SizedBox(height: AppSize.spaceBetweenSections(context)),
            _buildWelcomeContent(context),
            SizedBox(height: AppSize.spaceBetweenSections(context)),
            _buildStartButton(context, loginService),
          ],
        ),
      ),
    );
  }

  Widget _buildTabletLayout(BuildContext context, AuthService loginService) {
    return Center(
      child: SingleChildScrollView(
        padding: EdgeInsets.all(AppSize.defaultSpace(context)),
        child: Row(
          children: [
            Expanded(
              flex: 1,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  _buildLogo(context),
                  SizedBox(height: AppSize.spaceBetweenItems(context)),
                  _buildWelcomeContent(context),
                ],
              ),
            ),
            SizedBox(width: AppSize.spaceBetweenSections(context)),
            Expanded(
              flex: 1,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  _buildStartButton(context, loginService),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDesktopLayout(BuildContext context, AuthService loginService) {
    return Center(
      child: Container(
        constraints: const BoxConstraints(maxWidth: 1200),
        padding: EdgeInsets.all(AppSize.defaultSpace(context)),
        child: Row(
          children: [
            Expanded(
              flex: 2,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildWelcomeContent(context),
                  SizedBox(height: AppSize.spaceBetweenSections(context)),
                  _buildStartButton(context, loginService),
                ],
              ),
            ),
            SizedBox(width: AppSize.spaceBetweenSections(context)),
            Expanded(
              flex: 1,
              child: _buildLogo(context),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLogo(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(AppSize.radiusXl),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadow.withOpacity(0.1),
            blurRadius: 20,
            offset: const Offset(0, 10),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(AppSize.radiusXl),
        child: Image(
          image: AssetImage(dotenv.get('schoolLogo')),
          width: DevicesUtility.responsive(
            context,
            mobile: 200.0,
            tablet: 280.0,
            desktop: 320.0,
          ),
          height: DevicesUtility.responsive(
            context,
            mobile: 200.0,
            tablet: 280.0,
            desktop: 320.0,
          ),
          fit: BoxFit.cover,
        ),
      ),
    );
  }

  Widget _buildWelcomeContent(BuildContext context) {
    return Column(
      crossAxisAlignment: DevicesUtility.isDesktop(context)
          ? CrossAxisAlignment.start
          : CrossAxisAlignment.center,
      children: [
        ResponsiveText(
          'Bienvenue',
          style: Theme.of(context).textTheme.displayLarge?.copyWith(
            fontWeight: FontWeight.bold,
            color: AppColors.primary,
          ),
          textAlign: DevicesUtility.isDesktop(context)
              ? TextAlign.start
              : TextAlign.center,
        ),
        SizedBox(height: AppSize.spaceBetweenItems(context)),
        ResponsiveText(
          'Connectez-vous pour accéder à votre espace personnel',
          style: Theme.of(context).textTheme.bodyLarge?.copyWith(
            color: AppColors.textSecondary,
          ),
          textAlign: DevicesUtility.isDesktop(context)
              ? TextAlign.start
              : TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildStartButton(BuildContext context, AuthService loginService) {
    return Container(
      width: DevicesUtility.responsive(
        context,
        mobile: double.infinity,
        tablet: 300.0,
        desktop: 250.0,
      ),
      height: AppSize.buttonHeight(context),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          colors: AppColors.primaryGradient,
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(AppSize.buttonRadius),
        boxShadow: [
          BoxShadow(
            color: AppColors.primary.withOpacity(0.3),
            blurRadius: 15,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(AppSize.buttonRadius),
          onTap: () => _handleStartButtonTap(context, loginService),
          child: Center(
            child: ResponsiveText(
              AppText.Commencer,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: AppColors.onPrimary,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ),
      ),
    );
  }

  Future<void> _handleStartButtonTap(BuildContext context, AuthService loginService) async {
    if (!context.mounted) return;

    try {
      if (!await loginService.isAuthenticated()) {
        if (!context.mounted) return;
        showGeneralDialog(
          barrierDismissible: true,
          barrierLabel: 'Commencer',
          context: context,
          pageBuilder: (context, _, __) => const Center(
            child: LoginScreen(),
          ),
        );
      } else {
        await _navigateBasedOnRole(context, loginService);
      }
    } catch (e) {
      if (!context.mounted) return;
      _showErrorDialog(context, 'Une erreur est survenue: $e');
    }
  }

  Future<void> _navigateBasedOnRole(BuildContext context, AuthService loginService) async {
    if (!context.mounted) return;

    try {
      if (await loginService.isAdmin()) {
        if (!context.mounted) return;
        Navigator.pushReplacement(
          context,
          MaterialPageRoute(builder: (context) => AdminAppNavBar()),
        );
      } else if (await loginService.isParent()) {
        if (!context.mounted) return;
        Navigator.pushReplacement(
          context,
          MaterialPageRoute(builder: (context) => ParentAppNavBar()),
        );
      } else if (await loginService.isEnseiganat()) {
        if (!context.mounted) return;
        Navigator.pushReplacement(
          context,
          MaterialPageRoute(builder: (context) => THomeScreen()),
        );
      } else {
        if (!context.mounted) return;
        _showErrorDialog(context, 'Rôle utilisateur inconnu ou non autorisé.');
      }
    } catch (e) {
      if (!context.mounted) return;
      _showErrorDialog(context, 'Erreur lors de la navigation: $e');
    }
  }
  void _showErrorDialog(BuildContext context, String message) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text("Erreur"),
          content: Text(message),
          actions: <Widget>[
            TextButton(
              child: const Text("OK"),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
          ],
        );
      },
    );
  }
}
