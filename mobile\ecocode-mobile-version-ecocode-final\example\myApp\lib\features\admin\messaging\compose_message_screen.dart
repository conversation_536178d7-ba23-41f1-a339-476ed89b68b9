import 'package:flutter/material.dart';
import 'package:NovaSchool/features/admin/messaging/admin_messaging_screen.dart';

class AComposeMessageScreen extends StatefulWidget {
  final List<ContactItem> contacts;

  const AComposeMessageScreen({Key? key, required this.contacts}) : super(key: key);

  @override
  _AComposeMessageScreenState createState() => _AComposeMessageScreenState();
}

class _AComposeMessageScreenState extends State<AComposeMessageScreen> {
  final TextEditingController _subjectController = TextEditingController();
  final TextEditingController _messageController = TextEditingController();
  final TextEditingController _searchController = TextEditingController();
  
  List<ContactItem> _selectedContacts = [];
  List<ContactItem> _filteredContacts = [];
  String _selectedMessageType = 'individual';
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _filteredContacts = widget.contacts;
    _searchController.addListener(_filterContacts);
  }

  @override
  void dispose() {
    _subjectController.dispose();
    _messageController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  void _filterContacts() {
    final query = _searchController.text.toLowerCase();
    setState(() {
      _filteredContacts = widget.contacts.where((contact) {
        return contact.name.toLowerCase().contains(query) ||
               contact.type.toLowerCase().contains(query);
      }).toList();
    });
  }

  void _toggleContactSelection(ContactItem contact) {
    setState(() {
      if (_selectedContacts.contains(contact)) {
        _selectedContacts.remove(contact);
      } else {
        _selectedContacts.add(contact);
      }
    });
  }

  void _selectAllTeachers() {
    setState(() {
      final teachers = widget.contacts.where((c) => c.type == 'Enseignant').toList();
      _selectedContacts.clear();
      _selectedContacts.addAll(teachers);
    });
  }

  void _selectAllParents() {
    setState(() {
      final parents = widget.contacts.where((c) => c.type == 'Parent').toList();
      _selectedContacts.clear();
      _selectedContacts.addAll(parents);
    });
  }

  void _clearSelection() {
    setState(() {
      _selectedContacts.clear();
    });
  }

  Future<void> _sendMessage() async {
    if (_selectedContacts.isEmpty) {
      _showErrorDialog('Veuillez sélectionner au moins un destinataire');
      return;
    }

    if (_subjectController.text.trim().isEmpty) {
      _showErrorDialog('Veuillez saisir un objet');
      return;
    }

    if (_messageController.text.trim().isEmpty) {
      _showErrorDialog('Veuillez saisir un message');
      return;
    }

    setState(() => _isLoading = true);

    try {
      // TODO: Implement API call to send message
      await Future.delayed(const Duration(seconds: 2)); // Simulate API call
      
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Message envoyé à ${_selectedContacts.length} destinataire(s)'),
          backgroundColor: Colors.green,
        ),
      );
      
      Navigator.pop(context);
    } catch (e) {
      _showErrorDialog('Erreur lors de l\'envoi: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  void _showErrorDialog(String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Erreur'),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF2F2F2),
      appBar: PreferredSize(
        preferredSize: const Size.fromHeight(kToolbarHeight),
        child: Container(
          width: double.infinity,
          decoration: const BoxDecoration(
            gradient: LinearGradient(colors: [Colors.blue, Colors.cyan]),
          ),
          child: AppBar(
            backgroundColor: Colors.transparent,
            elevation: 0,
            iconTheme: const IconThemeData(
              color: Colors.white,
              size: 24,
            ),
            title: const Text(
              'Nouveau Message',
              style: TextStyle(
                color: Colors.white,
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            actions: [
              if (_isLoading)
                const Padding(
                  padding: EdgeInsets.all(16),
                  child: SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  ),
                )
              else
                TextButton(
                  onPressed: _sendMessage,
                  child: const Text(
                    'Envoyer',
                    style: TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
            ],
          ),
        ),
      ),
      body: Column(
        children: [
          // Message Type Selection
          Container(
            padding: const EdgeInsets.all(16),
            color: Colors.white,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Type de message:',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    Expanded(
                      child: RadioListTile<String>(
                        title: const Text('Individuel'),
                        value: 'individual',
                        groupValue: _selectedMessageType,
                        onChanged: (value) {
                          setState(() {
                            _selectedMessageType = value!;
                          });
                        },
                      ),
                    ),
                    Expanded(
                      child: RadioListTile<String>(
                        title: const Text('Diffusion'),
                        value: 'broadcast',
                        groupValue: _selectedMessageType,
                        onChanged: (value) {
                          setState(() {
                            _selectedMessageType = value!;
                          });
                        },
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          
          // Quick Selection Buttons
          Container(
            padding: const EdgeInsets.all(16),
            color: Colors.grey[100],
            child: Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _selectAllTeachers,
                    icon: const Icon(Icons.school, size: 16),
                    label: const Text('Tous les enseignants'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _selectAllParents,
                    icon: const Icon(Icons.family_restroom, size: 16),
                    label: const Text('Tous les parents'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.orange,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                IconButton(
                  onPressed: _clearSelection,
                  icon: const Icon(Icons.clear),
                  tooltip: 'Effacer la sélection',
                ),
              ],
            ),
          ),

          // Selected Contacts
          if (_selectedContacts.isNotEmpty)
            Container(
              padding: const EdgeInsets.all(16),
              color: Colors.blue[50],
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Destinataires sélectionnés (${_selectedContacts.length}):',
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 8),
                  Wrap(
                    spacing: 8,
                    runSpacing: 4,
                    children: _selectedContacts.map((contact) {
                      return Chip(
                        avatar: CircleAvatar(
                          backgroundColor: contact.type == 'Enseignant' ? Colors.green : Colors.orange,
                          child: Text(
                            contact.avatar,
                            style: const TextStyle(color: Colors.white, fontSize: 10),
                          ),
                        ),
                        label: Text(contact.name),
                        deleteIcon: const Icon(Icons.close, size: 16),
                        onDeleted: () => _toggleContactSelection(contact),
                      );
                    }).toList(),
                  ),
                ],
              ),
            ),

          // Search Bar
          Container(
            padding: const EdgeInsets.all(16),
            color: Colors.white,
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: 'Rechercher un contact...',
                prefixIcon: const Icon(Icons.search),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
          ),

          // Contacts List
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.all(8),
              itemCount: _filteredContacts.length,
              itemBuilder: (context, index) {
                final contact = _filteredContacts[index];
                final isSelected = _selectedContacts.contains(contact);
                
                return Card(
                  margin: const EdgeInsets.symmetric(vertical: 2),
                  child: ListTile(
                    leading: CircleAvatar(
                      backgroundColor: contact.type == 'Enseignant' ? Colors.green : Colors.orange,
                      child: Text(
                        contact.avatar,
                        style: const TextStyle(color: Colors.white, fontSize: 12),
                      ),
                    ),
                    title: Text(contact.name),
                    subtitle: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          contact.type,
                          style: TextStyle(
                            color: contact.type == 'Enseignant' ? Colors.green : Colors.orange,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        Text(contact.subject),
                      ],
                    ),
                    trailing: Checkbox(
                      value: isSelected,
                      onChanged: (_) => _toggleContactSelection(contact),
                    ),
                    onTap: () => _toggleContactSelection(contact),
                  ),
                );
              },
            ),
          ),

          // Message Form
          Container(
            padding: const EdgeInsets.all(16),
            color: Colors.white,
            child: Column(
              children: [
                TextField(
                  controller: _subjectController,
                  decoration: const InputDecoration(
                    labelText: 'Objet',
                    border: OutlineInputBorder(),
                  ),
                ),
                const SizedBox(height: 16),
                TextField(
                  controller: _messageController,
                  decoration: const InputDecoration(
                    labelText: 'Message',
                    border: OutlineInputBorder(),
                    alignLabelWithHint: true,
                  ),
                  maxLines: 4,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
