<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\shaders"/></dataSet><dataSet config="tacapes" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\tacapes\shaders"/></dataSet><dataSet config="debug" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\debug\shaders"/></dataSet><dataSet config="variant" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\tacapesDebug\shaders"/></dataSet></merger>