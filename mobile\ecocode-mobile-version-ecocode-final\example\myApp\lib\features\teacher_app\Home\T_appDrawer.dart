import 'package:NovaSchool/commun/parent_app/appBar_config.dart';
import 'package:NovaSchool/commun/teacher_app/app_TheadBar.dart';
import 'package:NovaSchool/commun/teacher_app/app_TnavBar.dart';
import 'package:NovaSchool/features/teacher_app/Home/home_Tscreen.dart';
import 'package:NovaSchool/features/teacher_app/Home/home_Twelcome.dart';
import 'package:NovaSchool/features/teacher_app/Home/pedagogicalElement/Emploi/empoi_TScreen.dart';
import 'package:NovaSchool/features/teacher_app/Home/pedagogicalElement/cours/cours_TScreen.dart';
import 'package:NovaSchool/features/teacher_app/Home/pedagogicalElement/exercice/exercice_TScreen.dart';
import 'package:NovaSchool/features/teacher_app/Home/pedagogicalElement/observations/observation_TScreen.dart';
import 'package:NovaSchool/features/teacher_app/Profil/profil_Tscreen.dart';
import 'package:NovaSchool/features/teacher_app/absence/absence_screen.dart';
import 'package:NovaSchool/features/teacher_app/student_management/student_management_screen.dart';
import 'package:NovaSchool/services/auth_service.dart';
import 'package:NovaSchool/services/fileDownloadService.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:NovaSchool/commun/teacher_app/app_TheadBar.dart';
import 'package:NovaSchool/features/teacher_app/Home/home_Twelcome.dart';
import 'package:NovaSchool/features/teacher_app/Home/pedagogicalElement/cours/cours_TScreen.dart';
import 'package:NovaSchool/features/teacher_app/Home/pedagogicalElement/exercice/exercice_TScreen.dart';
import 'package:NovaSchool/features/teacher_app/Home/pedagogicalElement/observations/observation_TScreen.dart';
import 'package:NovaSchool/features/teacher_app/Profil/profil_Tscreen.dart';
import 'package:NovaSchool/services/auth_service.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';

class TeacherDrawer extends StatefulWidget {
  @override
  _TeacherDrawerState createState() => _TeacherDrawerState();
}

class _TeacherDrawerState extends State<TeacherDrawer> {
  Future<Map<String, String>> _getUserInfo() async {
    return await AuthService().getSenderDetails();
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<Map<String, String>>(
      future: _getUserInfo(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return Drawer(
            child: Center(child: CircularProgressIndicator()),
          );
        } else if (snapshot.hasError) {
          return Drawer(
            child: Center(
              child: Text(
                'Erreur: ${snapshot.error}',
                style: TextStyle(color: Colors.red),
              ),
            ),
          );
        } else if (snapshot.hasData) {
          final userInfo = snapshot.data!;
          return Drawer(

            backgroundColor: Color(0xFFF2F2F2),
            child: Column(
              children: [
                Container(
                  padding: EdgeInsets.all(1),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(colors: [Colors.blue, Colors.cyan]),
                  ),
                  child: Column(
                    children: [
                    /***  AppBarConfig(
                        title: SizedBox(
                          //height: 120, // Exactement la hauteur de votre logo
                          child: Image.asset(
                            'assets/logos/ecocode.png',
                            color: Colors.white.withOpacity(0.89),
                            colorBlendMode: BlendMode.srcIn,
                            width: 120,
                            height: 120,
                          ),
                        ),

                      ),
***/

                      TAppHeadBar(
                        profilPage: TProfilScreen(
                          firstName: userInfo['senderUsername'] ?? '',
                          lastName: userInfo['senderUserSurname'] ?? '',
                          email: userInfo['email'] ?? '',
                          profile: userInfo['profile'] ?? '',
                        ),
                      ),

                      THomeWelcome(
                        firstName: userInfo['senderUsername'] ?? '',
                        lastName: userInfo['senderUserSurname'] ?? '',
                      ),
                      SizedBox(height: 20),
                    ],
                  ),
                ),
                SizedBox(height: 10),
                Expanded(
                  child: ListView(
                    padding: EdgeInsets.all(0),
                    children: [
                     _buildDrawerItem(context, Icons.house, "Accueil", TeacherAppNavBar()),//initialIndex: 0)),
                      /*_buildDrawerItem(
                        context,
                        Icons.person,
                        "Profil",
                        TProfilScreen(
                          firstName: userInfo['senderUsername'] ?? '',
                          lastName: userInfo['senderUserSurname'] ?? '',
                          email: userInfo['email'] ?? '',
                          profile: userInfo['profile'] ?? '',
                        ),
                      ),*/
                      Divider(),
                      _buildDrawerItem(context, Icons.book, "Cours", TCoursScreen()),
                      _buildDrawerItem(context, Icons.assignment, "Exercices", TExerciceScreen()),
                      _buildDrawerItem(context, Icons.visibility, "Observations", TObservationsScreen()),
                      _buildDrawerItem(context, Icons.schedule, "Emploi du temps", TEmploiScreen()),
                      Divider(),
                      _buildDrawerItem(context, Icons.person_off, "Gestion des Absences", TAbsenceScreen()),
                      _buildDrawerItem(context, Icons.people, "Gérer Élèves", TStudentManagementScreen()),

                    ],
                  ),
                ),
                Divider(),
                Padding(
                  padding: EdgeInsets.only(bottom: 16.0),
                  child: _buildDrawerItem(context, Icons.logout, "Déconnexion", null, isLogout: true),
                ),
                SizedBox(height: 15),
              ],
            ),
          );
        }
        return Drawer(child: Center(child: Text("Aucune donnée utilisateur trouvée.")));
      },
    );
  }

  Widget _buildDrawerItem(BuildContext context, IconData icon, String title, Widget? screen, {bool isLogout = false}) {
    return ListTile(
      leading: Icon(icon, color: Color(0xFF0993D7)),//Colors.lightBlue),
      title: Text(title, style: GoogleFonts.lato(fontSize: 16)),
      onTap: () async {
        if (isLogout) {
          AuthService.logoutKeycloak();
        } else if (screen != null) {
          // Si on clique sur "Accueil", on remet l'onglet Home
         /* if (title == "Accueil") {
            final controller = Get.find<TNavigationController>();
            controller.resetToHome();

            // On vide la pile jusqu'à la racine pour éviter les retours inattendus
            Navigator.of(context).popUntil((route) => route.isFirst);
          }*/if (title == "Accueil") {
            Navigator.push(
              context,
              MaterialPageRoute(builder: (context) => TeacherAppNavBar(initialIndex: 0))
                  //(route) => false, // supprime toutes les routes précédentes
            );
          }
          else {
            Navigator.push(
              context,
              MaterialPageRoute(builder: (context) => screen),
            );
          }
        }
      },
      trailing: Icon(Icons.arrow_forward_ios, size: 16, color: Colors.grey),
    );
  }
}
/*
class TeacherDrawer extends StatefulWidget {

  @override
  _TeacherDrawerState createState() => _TeacherDrawerState();
}

class _TeacherDrawerState extends State<TeacherDrawer> {
  /*Future<Map<String, String>> _getUserInfo() async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      return {
        'senderUsername': prefs.getString('senderUsername') ?? '',
        'senderUserSurname': prefs.getString('senderUserSurname') ?? '',
        'email': prefs.getString('email') ?? '',
        'profile': prefs.getString('profile') ?? '',
      };
    } catch (e) {
      print("Erreur lors de la récupération des infos utilisateur : $e");
      return {};
    }
  }*/
  Future<Map<String, String>> _getUserInfo() async {
    final authService = AuthService();
    return await authService.getSenderDetails();
  }
  Future<void> _saveUserInfo(Map<String, String> userInfo) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    await prefs.setString('senderUsername', userInfo['senderUsername'] ?? '');
    await prefs.setString('senderUserSurname', userInfo['senderUserSurname'] ?? '');
    await prefs.setString('email', userInfo['email'] ?? '');
    await prefs.setString('profile', userInfo['profile'] ?? '');
  }

  @override
  Widget build(BuildContext context) {
    //final userInfo = widget.userInfo; // Récupérer les infos passées
    return FutureBuilder<Map<String, String>>(
      future: _getUserInfo(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return Drawer(
            child: Center(child: CircularProgressIndicator()),
          );
        } else if (snapshot.hasError) {
          return Drawer(
            child: Center(
              child: Text(
                'Erreur: ${snapshot.error}',
                style: TextStyle(color: Colors.red),
              ),
            ),
          );
        } else if (snapshot.hasData) {
          final userInfo = snapshot.data!;
          return Drawer(
            backgroundColor: Color(0xFFF2F2F2),
            child: Column(
              children: [
                Container(
                  padding: EdgeInsets.all(1),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(colors: [Colors.blue, Colors.cyan]),
                  ),
                  child: Column(
                    children: [
                      TAppHeadBar(
                        profilPage: TProfilScreen(
                          firstName: userInfo['senderUsername'] ?? '',
                          lastName: userInfo['senderUserSurname'] ?? '',
                          email: userInfo['email'] ?? '',
                          profile: userInfo['profile'] ?? '',
                        ),
                      ),
                      THomeWelcome(
                        firstName: userInfo['senderUsername'] ?? '',
                        lastName: userInfo['senderUserSurname'] ?? '',
                      ),
                    ],
                  ),
                ),
                Expanded(
                  child: ListView(
                    padding: EdgeInsets.zero,
                    children: [
                      _buildDrawerItem(context, Icons.book, "Profil", TProfilScreen(firstName: userInfo['senderUsername'] ?? '',
                      lastName: userInfo['senderUserSurname'] ?? '',
                      email: userInfo['email'] ?? '',
                      profile: userInfo['profile'] ?? ''),),

                      _buildDrawerItem(context, Icons.book, "Cours", TCoursScreen()),
                      _buildDrawerItem(context, Icons.assignment, "Exercices", TExerciceScreen()),
                      _buildDrawerItem(context, Icons.visibility, "Observations", TObservationsScreen()),
                     ]
                  ),
                ),
                Divider(),
                Padding(
                  padding: EdgeInsets.only(bottom: 16.0),
                  child: _buildDrawerItem(context, Icons.logout, "Déconnexion", null, isLogout: true),
                ),
              ],
            ),
          );
        }
        return Drawer(child: Center(child: Text("Aucune donnée utilisateur trouvée.")));
      },
    );
  }

  Widget _buildDrawerItem(BuildContext context, IconData icon, String title, Widget? screen, {bool isLogout = false}) {
    return ListTile(
      leading: Icon(icon, color: Colors.lightBlue),
      title: Text(title, style: GoogleFonts.lato(fontSize: 16)),
      onTap: () async {
        if (isLogout) {
          AuthService.logoutKeycloak();
        } else if (screen != null) {
          Navigator.push(context, MaterialPageRoute(builder: (context) => screen));
        }
      },
      trailing: Icon(Icons.arrow_forward_ios, size: 16, color: Colors.grey),
    );
  }
}
*/