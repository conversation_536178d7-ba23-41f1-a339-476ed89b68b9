import 'package:NovaSchool/commun/admin_app/admin_navbar.dart';
import 'package:NovaSchool/commun/app_secondHeadBar.dart';
import 'package:NovaSchool/features/admin/chat/chat_scrren.dart';
import 'package:NovaSchool/features/admin/home/<USER>';
import 'package:NovaSchool/features/admin/home/<USER>';
import 'package:NovaSchool/features/admin/home/<USER>/cours/cours_Ascreen.dart';
import 'package:NovaSchool/features/admin/home/<USER>/discipline/discipline_Ascreen.dart';
import 'package:NovaSchool/features/admin/home/<USER>/exercice/exercice_Ascreen.dart';
import 'package:NovaSchool/features/admin/home/<USER>/observation/observation_Ascreen.dart';
import 'package:NovaSchool/features/admin/profil/profil_Ascreen.dart';
import 'package:NovaSchool/features/admin/user/utilisateur_screen.dart';
import 'package:NovaSchool/features/admin/messaging/admin_messaging_screen.dart';
import 'package:NovaSchool/features/teacher_app/Home/home_Tscreen.dart';
import 'package:NovaSchool/features/teacher_app/Home/pedagogicalElement/cours/cours_TScreen.dart';
import 'package:NovaSchool/features/teacher_app/Home/pedagogicalElement/exercice/exercice_TScreen.dart';
import 'package:NovaSchool/features/teacher_app/Profil/profil_Tscreen.dart';
import 'package:NovaSchool/services/auth_service.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:get/get.dart';

class AdminDrawer extends StatefulWidget {
  @override
  _AdminDrawerState createState() => _AdminDrawerState();
}

class _AdminDrawerState extends State<AdminDrawer> {
  //Map<String, String>? userInfo;
  //final ANavigationController controller = Get.put(ANavigationController());

  Future<Map<String, String>> _getUserInfo() async {
    return await AuthService().getSenderDetails();

  }


  @override
  Widget build(BuildContext context) {
    return FutureBuilder<Map<String, String>>(
      future: _getUserInfo(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return Drawer(
            child: Center(child: CircularProgressIndicator()),
          );
        } else if (snapshot.hasError) {
          return Drawer(
            child: Center(
              child: Text(
                'Erreur: ${snapshot.error}',
                style: TextStyle(color: Colors.red),
              ),
            ),
          );
        } else if (snapshot.hasData) {
          final userInfo = snapshot.data!;
          return Drawer(
            backgroundColor: Color(0xFFF2F2F2),
            child: Column(
              children: [
                Container(
                  padding: EdgeInsets.all(1),
                  decoration: BoxDecoration(
                    gradient:
                    LinearGradient(colors: [Colors.blue, Colors.cyan]),
                  ),
                  child: Column(
                    children: [
                      SAppHeadBar(
                        profilPage: AProfilScreen(
                          firstName: userInfo['senderUsername'] ?? '',
                          lastName: userInfo['senderUserSurname'] ?? '',
                          email: userInfo['email'] ?? '',
                          profile: userInfo['profile'] ?? '',
                        ),
                      ),
                      AHomeWelcome(
                        firstName: userInfo['senderUsername'] ?? '',
                        lastName: userInfo['senderUserSurname'] ?? '',
                      ),
                      SizedBox(height: 20),
                    ],
                  ),
                ),
                SizedBox(height: 10),
                Expanded(
                  child: ListView(
                    padding: EdgeInsets.all(0),
                    children: [
                      _buildDrawerItem(
                          context, Icons.house, "Accueil", AdminAppNavBar()),
                      /* _buildDrawerItem(
                        context,
                        Icons.person,
                        "Profil",
                        AProfilScreen(
                          firstName: userInfo['senderUsername'] ?? '',
                          lastName: userInfo['senderUserSurname'] ?? '',
                          email: userInfo['email'] ?? '',
                          profile: userInfo['profile'] ?? '',
                        ),
                      ),*/
                      /*_buildDrawerItem(
                          context, Icons.house, "chat", ChatScreen() ),*/
                      Divider(),
                      _buildDrawerItem(
                          context, Icons.book, "Cours", ACoursScreen()),
                      _buildDrawerItem(context, Icons.assignment, "Exercices",
                          AExerciceScreen()),
                      _buildDrawerItem(context, Icons.visibility,
                          "Observations", AObservationScreen()),
                      _buildDrawerItem(context, Icons.warning, "Disciplines",
                          ADisciplineScreen()),
                      _buildDrawerItem(
                          context, Icons.supervised_user_circle, "Utilisateurs",
                          UserManagementPage()),
                      Divider(),
                      _buildDrawerItem(context, Icons.message, "Messagerie",
                          AMessagingScreen()),
                    ],
                  ),
                ),
                Divider(),
                Padding(
                  padding: EdgeInsets.only(bottom: 16.0),
                  child: _buildDrawerItem(
                      context, Icons.logout, "Déconnexion", null,
                      isLogout: true),
                ),
                SizedBox(height: 15),
              ],
            ),
          );
        }
        return Drawer(
            child: Center(child: Text("Aucune donnée utilisateur trouvée.")));
      },
    );
  }

  /*Widget _buildDrawerItem(BuildContext context, IconData icon, String title, Widget? screen, {bool isLogout = false}) {
    return ListTile(
      leading: Icon(icon, color: Color(0xFF0993D7)),//Colors.lightBlue),
      title: Text(title, style: GoogleFonts.lato(fontSize: 16)),
      onTap: () async {
        if (isLogout) {
          AuthService.logoutKeycloak();
        } else if (screen != null) {
          // Si on clique sur "Accueil", on remet l'onglet Home
          /* if (title == "Accueil") {
            final controller = Get.find<TNavigationController>();
            controller.resetToHome();

            // On vide la pile jusqu'à la racine pour éviter les retours inattendus
            Navigator.of(context).popUntil((route) => route.isFirst);
          }*/if (title == "Accueil") {
            Navigator.push(
                context,
                MaterialPageRoute(builder: (context) => AdminAppNavBar(initialIndex: 0))
              //(route) => false, // supprime toutes les routes précédentes
            );
          }
          else {
            Navigator.push(
              context,
              MaterialPageRoute(builder: (context) => screen),
            );
          }
        }
      },
      trailing: Icon(Icons.arrow_forward_ios, size: 16, color: Colors.grey),
    );
  }*/
  /*Widget _buildDrawerItem(BuildContext context, IconData icon, String title,
      Widget? screen, {
        bool isLogout = false,
      }) {
    return ListTile(
      leading: Icon(icon, color: Color(0xFF0993D7)),
      title: Text(title, style: GoogleFonts.lato(fontSize: 16)),
      onTap: () async {
        Navigator.pop(context); // ✅ Ferme le Drawer d'abord

        if (isLogout) {
          AuthService.logoutKeycloak();
        } else if (screen != null) {
          if (title == "Accueil") {
            // ✅ Remplacer la navigation pour éviter rechargement multiple
            Navigator.pushReplacement(
              context,
              MaterialPageRoute(builder: (_) => AdminAppNavBar(initialIndex: 0)),
            );
          } else {
            Navigator.push(
              context,
              MaterialPageRoute(builder: (_) => screen),
            );
          }
        }
      },
      trailing: Icon(Icons.arrow_forward_ios, size: 16, color: Colors.grey),
    );
  }
*//*
  Widget _buildDrawerItem(BuildContext context, IconData icon, String title,
      Widget? screen,
      {bool isLogout = false,bool isHome = false}) {
    return ListTile(
      leading: Icon(icon, color: Color(0xFF0993D7)), //Colors.lightBlue),
      title: Text(title, style: GoogleFonts.lato(fontSize: 16)),
      onTap: () async {
        if (isLogout) {
          AuthService.logoutKeycloak();
        } else if (screen != null) {
          if (isHome) {
            controller.resetToHome(); // ✅ Reset l'index vers PHomeScreen
          }
          Navigator.push(
              context, MaterialPageRoute(builder: (context) => screen));
        }
      },
      trailing: Icon(Icons.arrow_forward_ios, size: 16, color: Colors.grey),
    );
  }*/

  Widget _buildDrawerItem(BuildContext context, IconData icon, String title, Widget? screen, {bool isLogout = false}) {
    return ListTile(
      leading: Icon(icon, color: Color(0xFF0993D7)),//Colors.lightBlue),
      title: Text(title, style: GoogleFonts.lato(fontSize: 16)),
      onTap: () async {
        if (isLogout) {
          AuthService.logoutKeycloak();
        } else if (screen != null) {
          // Si on clique sur "Accueil", on remet l'onglet Home
          /* if (title == "Accueil") {
            final controller = Get.find<TNavigationController>();
            controller.resetToHome();

            // On vide la pile jusqu'à la racine pour éviter les retours inattendus
            Navigator.of(context).popUntil((route) => route.isFirst);
          }*/if (title == "Accueil") {
            Navigator.push(
                context,
                MaterialPageRoute(builder: (context) => AdminAppNavBar(initialIndex: 0))
              //(route) => false, // supprime toutes les routes précédentes
            );
          }
          else {
            Navigator.push(
              context,
              MaterialPageRoute(builder: (context) => screen),
            );
          }
        }
      },
      trailing: Icon(Icons.arrow_forward_ios, size: 16, color: Colors.grey),
    );
  }
}