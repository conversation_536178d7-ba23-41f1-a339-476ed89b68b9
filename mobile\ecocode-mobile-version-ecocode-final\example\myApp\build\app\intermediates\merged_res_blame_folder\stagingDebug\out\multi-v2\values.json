{"logs": [{"outputFile": "com.example.NovaSchool.app-mergeStagingDebugResources-51:/values/values.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\42462ff615a470fce269a489eb930f99\\transformed\\core-1.13.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,98,99,103,104,105,106,112,122,155,176,209", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,187,275,340,406,475,538,608,676,748,818,879,953,1026,1087,1148,1210,1274,1336,1397,1465,1565,1625,1691,1764,1833,1890,1942,2004,2076,2152,2217,2276,2335,2395,2455,2515,2575,2635,2695,2755,2815,2875,2935,2994,3054,3114,3174,3234,3294,3354,3414,3474,3534,3594,3653,3713,3773,3832,3891,3950,4009,4068,4127,4162,4197,4252,4315,4370,4428,4486,4547,4610,4667,4718,4768,4829,4886,4952,4986,5021,5056,5126,5193,5265,5334,5403,5477,5549,5637,5708,5825,6026,6136,6337,6466,6538,6605,6808,7109,8840,9521,10203", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,97,98,102,103,104,105,111,121,154,175,208,214", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "110,182,270,335,401,470,533,603,671,743,813,874,948,1021,1082,1143,1205,1269,1331,1392,1460,1560,1620,1686,1759,1828,1885,1937,1999,2071,2147,2212,2271,2330,2390,2450,2510,2570,2630,2690,2750,2810,2870,2930,2989,3049,3109,3169,3229,3289,3349,3409,3469,3529,3589,3648,3708,3768,3827,3886,3945,4004,4063,4122,4157,4192,4247,4310,4365,4423,4481,4542,4605,4662,4713,4763,4824,4881,4947,4981,5016,5051,5121,5188,5260,5329,5398,5472,5544,5632,5703,5820,6021,6131,6332,6461,6533,6600,6803,7104,8835,9516,10198,10365"}, "to": {"startLines": "29,70,71,88,89,120,121,227,228,229,230,231,232,233,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,328,329,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,375,405,406,407,408,409,410,411,442,1984,1985,1990,1993,1998,2153,2154,2819,2864,3034,3067,3097,3130", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "985,2715,2787,4084,4149,6239,6308,13527,13597,13665,13737,13807,13868,13942,15185,15246,15307,15369,15433,15495,15556,15624,15724,15784,15850,15923,15992,16049,16101,17261,17333,17409,17474,17533,17592,17652,17712,17772,17832,17892,17952,18012,18072,18132,18192,18251,18311,18371,18431,18491,18551,18611,18671,18731,18791,18851,18910,18970,19030,19089,19148,19207,19266,19325,19893,19928,20514,20569,20632,20687,20745,20803,20864,20927,20984,21035,21085,21146,21203,21269,21303,21338,22379,24467,24534,24606,24675,24744,24818,24890,28008,128557,128674,128941,129234,129501,141636,141708,163587,165561,173396,175127,176127,176809", "endLines": "29,70,71,88,89,120,121,227,228,229,230,231,232,233,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,328,329,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,375,405,406,407,408,409,410,411,442,1984,1988,1990,1996,1998,2153,2154,2824,2873,3066,3087,3129,3135", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "1040,2782,2870,4144,4210,6303,6366,13592,13660,13732,13802,13863,13937,14010,15241,15302,15364,15428,15490,15551,15619,15719,15779,15845,15918,15987,16044,16096,16158,17328,17404,17469,17528,17587,17647,17707,17767,17827,17887,17947,18007,18067,18127,18187,18246,18306,18366,18426,18486,18546,18606,18666,18726,18786,18846,18905,18965,19025,19084,19143,19202,19261,19320,19379,19923,19958,20564,20627,20682,20740,20798,20859,20922,20979,21030,21080,21141,21198,21264,21298,21333,21368,22444,24529,24601,24670,24739,24813,24885,24973,28074,128669,128870,129046,129430,129625,141703,141770,163785,165857,175122,175803,176804,176971"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\57c1c0bacecfc631670f89f9a212e268\\transformed\\appcompat-1.6.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,221,222,226,230,234,239,245,252,256,260,265,269,273,277,281,285,289,295,299,305,309,315,319,324,328,331,335,341,345,351,355,361,364,368,372,376,380,384,385,386,387,390,393,396,399,403,404,405,406,407,410,412,414,416,421,422,426,432,436,437,439,451,452,456,462,466,467,468,472,499,503,504,508,536,708,734,905,931,962,970,976,992,1014,1019,1024,1034,1043,1052,1056,1063,1082,1089,1090,1099,1102,1105,1109,1113,1117,1120,1121,1126,1131,1141,1146,1153,1159,1160,1163,1167,1172,1174,1176,1179,1182,1184,1188,1191,1198,1201,1204,1208,1210,1214,1216,1218,1220,1224,1232,1240,1252,1258,1267,1270,1281,1284,1285,1290,1291,1296,1365,1435,1436,1446,1455,1456,1458,1462,1465,1468,1471,1474,1477,1480,1483,1487,1490,1493,1496,1500,1503,1507,1511,1512,1513,1514,1515,1516,1517,1518,1519,1520,1521,1522,1523,1524,1525,1526,1527,1528,1529,1530,1531,1533,1535,1536,1537,1538,1539,1540,1541,1542,1544,1545,1547,1548,1550,1552,1553,1555,1556,1557,1558,1559,1560,1562,1563,1564,1565,1566,1567,1569,1571,1573,1574,1575,1576,1577,1578,1579,1580,1581,1582,1583,1584,1585,1587,1588,1589,1590,1591,1592,1593,1595,1599,1603,1604,1605,1606,1607,1608,1612,1613,1614,1615,1617,1619,1621,1623,1625,1626,1627,1628,1630,1632,1634,1635,1636,1637,1638,1639,1640,1641,1642,1643,1644,1645,1648,1649,1650,1651,1653,1655,1656,1658,1659,1661,1663,1665,1666,1667,1668,1669,1670,1671,1672,1673,1674,1675,1676,1678,1679,1680,1681,1683,1684,1685,1686,1687,1689,1691,1693,1695,1696,1697,1698,1699,1700,1701,1702,1703,1704,1705,1706,1707,1708,1709,1710,1785,1788,1791,1794,1808,1814,1824,1827,1856,1883,1892,1956,2319,2323,2351,2379,2397,2421,2427,2433,2454,2578,2598,2604,2608,2614,2649,2661,2727,2747,2802,2814,2840", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,160,205,254,295,350,412,476,546,607,682,758,835,913,998,1080,1156,1232,1309,1387,1493,1599,1678,1758,1815,1873,1947,2022,2087,2153,2213,2274,2346,2419,2486,2554,2613,2672,2731,2790,2849,2903,2957,3010,3064,3118,3172,3226,3300,3379,3452,3526,3597,3669,3741,3814,3871,3929,4002,4076,4150,4225,4297,4370,4440,4511,4571,4632,4701,4770,4840,4914,4990,5054,5131,5207,5284,5349,5418,5495,5570,5639,5707,5784,5850,5911,6008,6073,6142,6241,6312,6371,6429,6486,6545,6609,6680,6752,6824,6896,6968,7035,7103,7171,7230,7293,7357,7447,7538,7598,7664,7731,7797,7867,7931,7984,8051,8112,8179,8292,8350,8413,8478,8543,8618,8691,8763,8807,8854,8900,8949,9010,9071,9132,9194,9258,9322,9386,9451,9514,9574,9635,9701,9760,9820,9882,9953,10013,10081,10167,10254,10344,10431,10519,10601,10684,10774,10865,10917,10975,11020,11086,11150,11207,11264,11318,11375,11423,11472,11523,11557,11604,11653,11699,11731,11795,11857,11917,11974,12048,12118,12196,12250,12320,12405,12453,12499,12560,12623,12689,12753,12824,12887,12952,13016,13077,13138,13190,13263,13337,13406,13481,13555,13629,13770,13840,13893,13971,14061,14149,14245,14335,14917,15006,15253,15534,15786,16071,16464,16941,17163,17385,17661,17888,18118,18348,18578,18808,19035,19454,19680,20105,20335,20763,20982,21265,21473,21604,21831,22257,22482,22909,23130,23555,23675,23951,24252,24576,24867,25181,25318,25449,25554,25796,25963,26167,26375,26646,26758,26870,26975,27092,27306,27452,27592,27678,28026,28114,28360,28778,29027,29109,29207,29864,29964,30216,30640,30895,30989,31078,31315,33339,33581,33683,33936,36092,46773,48289,58984,60512,62269,62895,63315,64576,65841,66097,66333,66880,67374,67979,68177,68757,70125,70500,70618,71156,71313,71509,71782,72038,72208,72349,72413,72778,73145,73821,74085,74423,74776,74870,75056,75362,75624,75749,75876,76115,76326,76445,76638,76815,77270,77451,77573,77832,77945,78132,78234,78341,78470,78745,79253,79749,80626,80920,81490,81639,82371,82543,82627,82963,83055,83333,88564,93935,93997,94575,95159,95250,95363,95592,95752,95904,96075,96241,96410,96577,96740,96983,97153,97326,97497,97771,97970,98175,98505,98589,98685,98781,98879,98979,99081,99183,99285,99387,99489,99589,99685,99797,99926,100049,100180,100311,100409,100523,100617,100757,100891,100987,101099,101199,101315,101411,101523,101623,101763,101899,102063,102193,102351,102501,102642,102786,102921,103033,103183,103311,103439,103575,103707,103837,103967,104079,104219,104365,104509,104647,104713,104803,104879,104983,105073,105175,105283,105391,105491,105571,105663,105761,105871,105923,106001,106107,106199,106303,106413,106535,106698,106855,106935,107035,107125,107235,107325,107566,107660,107766,107858,107958,108070,108184,108300,108416,108510,108624,108736,108838,108958,109080,109162,109266,109386,109512,109610,109704,109792,109904,110020,110142,110254,110429,110545,110631,110723,110835,110959,111026,111152,111220,111348,111492,111620,111689,111784,111899,112012,112111,112220,112331,112442,112543,112648,112748,112878,112969,113092,113186,113298,113384,113488,113584,113672,113790,113894,113998,114124,114212,114320,114420,114510,114620,114704,114806,114890,114944,115008,115114,115200,115310,115394,115514,118130,118248,118363,118443,118804,119037,119554,119632,120976,122337,122725,125568,135621,135756,137126,138483,139055,139806,140068,140268,140647,144925,145531,145760,145911,146126,147209,147521,150547,151291,153422,153762,155073", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,220,221,225,229,233,238,244,251,255,259,264,268,272,276,280,284,288,294,298,304,308,314,318,323,327,330,334,340,344,350,354,360,363,367,371,375,379,383,384,385,386,389,392,395,398,402,403,404,405,406,409,411,413,415,420,421,425,431,435,436,438,450,451,455,461,465,466,467,471,498,502,503,507,535,707,733,904,930,961,969,975,991,1013,1018,1023,1033,1042,1051,1055,1062,1081,1088,1089,1098,1101,1104,1108,1112,1116,1119,1120,1125,1130,1140,1145,1152,1158,1159,1162,1166,1171,1173,1175,1178,1181,1183,1187,1190,1197,1200,1203,1207,1209,1213,1215,1217,1219,1223,1231,1239,1251,1257,1266,1269,1280,1283,1284,1289,1290,1295,1364,1434,1435,1445,1454,1455,1457,1461,1464,1467,1470,1473,1476,1479,1482,1486,1489,1492,1495,1499,1502,1506,1510,1511,1512,1513,1514,1515,1516,1517,1518,1519,1520,1521,1522,1523,1524,1525,1526,1527,1528,1529,1530,1532,1534,1535,1536,1537,1538,1539,1540,1541,1543,1544,1546,1547,1549,1551,1552,1554,1555,1556,1557,1558,1559,1561,1562,1563,1564,1565,1566,1568,1570,1572,1573,1574,1575,1576,1577,1578,1579,1580,1581,1582,1583,1584,1586,1587,1588,1589,1590,1591,1592,1594,1598,1602,1603,1604,1605,1606,1607,1611,1612,1613,1614,1616,1618,1620,1622,1624,1625,1626,1627,1629,1631,1633,1634,1635,1636,1637,1638,1639,1640,1641,1642,1643,1644,1647,1648,1649,1650,1652,1654,1655,1657,1658,1660,1662,1664,1665,1666,1667,1668,1669,1670,1671,1672,1673,1674,1675,1677,1678,1679,1680,1682,1683,1684,1685,1686,1688,1690,1692,1694,1695,1696,1697,1698,1699,1700,1701,1702,1703,1704,1705,1706,1707,1708,1709,1784,1787,1790,1793,1807,1813,1823,1826,1855,1882,1891,1955,2318,2322,2350,2378,2396,2420,2426,2432,2453,2577,2597,2603,2607,2613,2648,2660,2726,2746,2801,2813,2839,2846", "endColumns": "54,44,48,40,54,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,43,46,45,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "155,200,249,290,345,407,471,541,602,677,753,830,908,993,1075,1151,1227,1304,1382,1488,1594,1673,1753,1810,1868,1942,2017,2082,2148,2208,2269,2341,2414,2481,2549,2608,2667,2726,2785,2844,2898,2952,3005,3059,3113,3167,3221,3295,3374,3447,3521,3592,3664,3736,3809,3866,3924,3997,4071,4145,4220,4292,4365,4435,4506,4566,4627,4696,4765,4835,4909,4985,5049,5126,5202,5279,5344,5413,5490,5565,5634,5702,5779,5845,5906,6003,6068,6137,6236,6307,6366,6424,6481,6540,6604,6675,6747,6819,6891,6963,7030,7098,7166,7225,7288,7352,7442,7533,7593,7659,7726,7792,7862,7926,7979,8046,8107,8174,8287,8345,8408,8473,8538,8613,8686,8758,8802,8849,8895,8944,9005,9066,9127,9189,9253,9317,9381,9446,9509,9569,9630,9696,9755,9815,9877,9948,10008,10076,10162,10249,10339,10426,10514,10596,10679,10769,10860,10912,10970,11015,11081,11145,11202,11259,11313,11370,11418,11467,11518,11552,11599,11648,11694,11726,11790,11852,11912,11969,12043,12113,12191,12245,12315,12400,12448,12494,12555,12618,12684,12748,12819,12882,12947,13011,13072,13133,13185,13258,13332,13401,13476,13550,13624,13765,13835,13888,13966,14056,14144,14240,14330,14912,15001,15248,15529,15781,16066,16459,16936,17158,17380,17656,17883,18113,18343,18573,18803,19030,19449,19675,20100,20330,20758,20977,21260,21468,21599,21826,22252,22477,22904,23125,23550,23670,23946,24247,24571,24862,25176,25313,25444,25549,25791,25958,26162,26370,26641,26753,26865,26970,27087,27301,27447,27587,27673,28021,28109,28355,28773,29022,29104,29202,29859,29959,30211,30635,30890,30984,31073,31310,33334,33576,33678,33931,36087,46768,48284,58979,60507,62264,62890,63310,64571,65836,66092,66328,66875,67369,67974,68172,68752,70120,70495,70613,71151,71308,71504,71777,72033,72203,72344,72408,72773,73140,73816,74080,74418,74771,74865,75051,75357,75619,75744,75871,76110,76321,76440,76633,76810,77265,77446,77568,77827,77940,78127,78229,78336,78465,78740,79248,79744,80621,80915,81485,81634,82366,82538,82622,82958,83050,83328,88559,93930,93992,94570,95154,95245,95358,95587,95747,95899,96070,96236,96405,96572,96735,96978,97148,97321,97492,97766,97965,98170,98500,98584,98680,98776,98874,98974,99076,99178,99280,99382,99484,99584,99680,99792,99921,100044,100175,100306,100404,100518,100612,100752,100886,100982,101094,101194,101310,101406,101518,101618,101758,101894,102058,102188,102346,102496,102637,102781,102916,103028,103178,103306,103434,103570,103702,103832,103962,104074,104214,104360,104504,104642,104708,104798,104874,104978,105068,105170,105278,105386,105486,105566,105658,105756,105866,105918,105996,106102,106194,106298,106408,106530,106693,106850,106930,107030,107120,107230,107320,107561,107655,107761,107853,107953,108065,108179,108295,108411,108505,108619,108731,108833,108953,109075,109157,109261,109381,109507,109605,109699,109787,109899,110015,110137,110249,110424,110540,110626,110718,110830,110954,111021,111147,111215,111343,111487,111615,111684,111779,111894,112007,112106,112215,112326,112437,112538,112643,112743,112873,112964,113087,113181,113293,113379,113483,113579,113667,113785,113889,113993,114119,114207,114315,114415,114505,114615,114699,114801,114885,114939,115003,115109,115195,115305,115389,115509,118125,118243,118358,118438,118799,119032,119549,119627,120971,122332,122720,125563,135616,135751,137121,138478,139050,139801,140063,140263,140642,144920,145526,145755,145906,146121,147204,147516,150542,151286,153417,153757,155068,155271"}, "to": {"startLines": "4,27,28,59,60,61,63,64,65,66,67,68,69,72,73,74,75,76,77,78,79,80,81,86,87,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,234,235,239,240,241,242,243,244,245,275,276,277,278,279,280,281,282,318,319,320,321,326,334,335,340,362,368,369,370,371,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,441,446,447,448,449,450,451,459,460,464,468,472,477,483,490,494,498,503,507,511,515,519,523,527,533,537,543,547,553,557,562,566,569,573,579,583,589,593,599,602,606,610,614,618,622,623,624,625,628,631,634,637,641,642,643,644,645,648,650,652,654,659,660,664,670,674,675,677,689,690,694,700,704,705,706,710,737,741,742,746,774,946,972,1143,1169,1200,1208,1214,1230,1252,1257,1262,1272,1281,1290,1294,1301,1320,1327,1328,1337,1340,1343,1347,1351,1355,1358,1359,1364,1369,1379,1384,1391,1397,1398,1401,1405,1410,1412,1414,1417,1420,1422,1426,1429,1436,1439,1442,1446,1448,1452,1454,1456,1458,1462,1470,1478,1490,1496,1505,1508,1519,1522,1523,1528,1529,1562,1631,1701,1702,1712,1721,1873,1875,1879,1882,1885,1888,1891,1894,1897,1900,1904,1907,1910,1913,1917,1920,1924,1928,1929,1930,1931,1932,1933,1934,1935,1936,1937,1938,1939,1940,1941,1942,1943,1944,1945,1946,1947,1948,1950,1952,1953,1954,1955,1956,1957,1958,1959,1961,1962,1964,1965,1967,1969,1970,1972,1973,1974,1975,1976,1977,1979,1980,1981,1982,1983,2000,2002,2004,2006,2007,2008,2009,2010,2011,2012,2013,2014,2015,2016,2017,2018,2020,2021,2022,2023,2024,2025,2026,2028,2032,2046,2047,2048,2049,2050,2051,2055,2056,2057,2058,2060,2062,2064,2066,2068,2069,2070,2071,2073,2075,2077,2078,2079,2080,2081,2082,2083,2084,2085,2086,2087,2088,2091,2092,2093,2094,2096,2098,2099,2101,2102,2104,2106,2108,2109,2110,2111,2112,2113,2114,2115,2116,2117,2118,2119,2121,2122,2123,2124,2126,2127,2128,2129,2130,2132,2134,2136,2138,2139,2140,2141,2142,2143,2144,2145,2146,2147,2148,2149,2150,2151,2152,2158,2233,2236,2239,2242,2256,2273,2315,2318,2347,2374,2383,2447,2815,2836,2874,3012,3136,3160,3166,3195,3216,3340,3368,3374,3518,3544,3611,3682,3782,3802,3857,3869,3895", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "225,891,936,1983,2024,2079,2214,2278,2348,2409,2484,2560,2637,2875,2960,3042,3118,3194,3271,3349,3455,3561,3640,3969,4026,4886,4960,5035,5100,5166,5226,5287,5359,5432,5499,5567,5626,5685,5744,5803,5862,5916,5970,6023,6077,6131,6185,6529,6603,6682,6755,6829,6900,6972,7044,7117,7174,7232,7305,7379,7453,7528,7600,7673,7743,7814,7874,7935,8004,8073,8143,8217,8293,8357,8434,8510,8587,8652,8721,8798,8873,8942,9010,9087,9153,9214,9311,9376,9445,9544,9615,9674,9732,9789,9848,9912,9983,10055,10127,10199,10271,10338,10406,10474,10533,10596,10660,10750,10841,10901,10967,11034,11100,11170,11234,11287,11354,11415,11482,11595,11653,11716,11781,11846,11921,11994,12066,12110,12157,12203,12252,12313,12374,12435,12497,12561,12625,12689,12754,12817,12877,12938,13004,13063,13123,13185,13256,13316,14015,14101,14351,14441,14528,14616,14698,14781,14871,16808,16860,16918,16963,17029,17093,17150,17207,19384,19441,19489,19538,19793,20163,20210,20468,21639,21942,22006,22068,22128,22449,22523,22593,22671,22725,22795,22880,22928,22974,23035,23098,23164,23228,23299,23362,23427,23491,23552,23613,23665,23738,23812,23881,23956,24030,24104,24245,27955,28316,28394,28484,28572,28668,28758,29340,29429,29676,29957,30209,30494,30887,31364,31586,31808,32084,32311,32541,32771,33001,33231,33458,33877,34103,34528,34758,35186,35405,35688,35896,36027,36254,36680,36905,37332,37553,37978,38098,38374,38675,38999,39290,39604,39741,39872,39977,40219,40386,40590,40798,41069,41181,41293,41398,41515,41729,41875,42015,42101,42449,42537,42783,43201,43450,43532,43630,44287,44387,44639,45063,45318,45412,45501,45738,47762,48004,48106,48359,50515,61196,62712,73407,74935,76692,77318,77738,78999,80264,80520,80756,81303,81797,82402,82600,83180,84548,84923,85041,85579,85736,85932,86205,86461,86631,86772,86836,87201,87568,88244,88508,88846,89199,89293,89479,89785,90047,90172,90299,90538,90749,90868,91061,91238,91693,91874,91996,92255,92368,92555,92657,92764,92893,93168,93676,94172,95049,95343,95913,96062,96794,96966,97050,97386,97478,99815,105046,110417,110479,111057,111641,119588,119701,119930,120090,120242,120413,120579,120748,120915,121078,121321,121491,121664,121835,122109,122308,122513,122843,122927,123023,123119,123217,123317,123419,123521,123623,123725,123827,123927,124023,124135,124264,124387,124518,124649,124747,124861,124955,125095,125229,125325,125437,125537,125653,125749,125861,125961,126101,126237,126401,126531,126689,126839,126980,127124,127259,127371,127521,127649,127777,127913,128045,128175,128305,128417,129697,129843,129987,130125,130191,130281,130357,130461,130551,130653,130761,130869,130969,131049,131141,131239,131349,131401,131479,131585,131677,131781,131891,132013,132176,132977,133057,133157,133247,133357,133447,133688,133782,133888,133980,134080,134192,134306,134422,134538,134632,134746,134858,134960,135080,135202,135284,135388,135508,135634,135732,135826,135914,136026,136142,136264,136376,136551,136667,136753,136845,136957,137081,137148,137274,137342,137470,137614,137742,137811,137906,138021,138134,138233,138342,138453,138564,138665,138770,138870,139000,139091,139214,139308,139420,139506,139610,139706,139794,139912,140016,140120,140246,140334,140442,140542,140632,140742,140826,140928,141012,141066,141130,141236,141322,141432,141516,141920,144536,144654,144769,144849,145210,145796,147200,147278,148622,149983,150371,153214,163452,164191,165862,172675,176976,177727,177989,178836,179215,183493,184347,184576,189184,190194,192146,194546,198670,199414,201545,201885,203196", "endLines": "4,27,28,59,60,61,63,64,65,66,67,68,69,72,73,74,75,76,77,78,79,80,81,86,87,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,234,235,239,240,241,242,243,244,245,275,276,277,278,279,280,281,282,318,319,320,321,326,334,335,340,362,368,369,370,371,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,441,446,447,448,449,450,458,459,463,467,471,476,482,489,493,497,502,506,510,514,518,522,526,532,536,542,546,552,556,561,565,568,572,578,582,588,592,598,601,605,609,613,617,621,622,623,624,627,630,633,636,640,641,642,643,644,647,649,651,653,658,659,663,669,673,674,676,688,689,693,699,703,704,705,709,736,740,741,745,773,945,971,1142,1168,1199,1207,1213,1229,1251,1256,1261,1271,1280,1289,1293,1300,1319,1326,1327,1336,1339,1342,1346,1350,1354,1357,1358,1363,1368,1378,1383,1390,1396,1397,1400,1404,1409,1411,1413,1416,1419,1421,1425,1428,1435,1438,1441,1445,1447,1451,1453,1455,1457,1461,1469,1477,1489,1495,1504,1507,1518,1521,1522,1527,1528,1533,1630,1700,1701,1711,1720,1721,1874,1878,1881,1884,1887,1890,1893,1896,1899,1903,1906,1909,1912,1916,1919,1923,1927,1928,1929,1930,1931,1932,1933,1934,1935,1936,1937,1938,1939,1940,1941,1942,1943,1944,1945,1946,1947,1949,1951,1952,1953,1954,1955,1956,1957,1958,1960,1961,1963,1964,1966,1968,1969,1971,1972,1973,1974,1975,1976,1978,1979,1980,1981,1982,1983,2001,2003,2005,2006,2007,2008,2009,2010,2011,2012,2013,2014,2015,2016,2017,2019,2020,2021,2022,2023,2024,2025,2027,2031,2035,2046,2047,2048,2049,2050,2054,2055,2056,2057,2059,2061,2063,2065,2067,2068,2069,2070,2072,2074,2076,2077,2078,2079,2080,2081,2082,2083,2084,2085,2086,2087,2090,2091,2092,2093,2095,2097,2098,2100,2101,2103,2105,2107,2108,2109,2110,2111,2112,2113,2114,2115,2116,2117,2118,2120,2121,2122,2123,2125,2126,2127,2128,2129,2131,2133,2135,2137,2138,2139,2140,2141,2142,2143,2144,2145,2146,2147,2148,2149,2150,2151,2152,2232,2235,2238,2241,2255,2261,2282,2317,2346,2373,2382,2446,2809,2818,2863,2901,3029,3159,3165,3171,3215,3339,3359,3373,3377,3523,3578,3622,3747,3801,3856,3868,3894,3901", "endColumns": "54,44,48,40,54,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,43,46,45,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "275,931,980,2019,2074,2136,2273,2343,2404,2479,2555,2632,2710,2955,3037,3113,3189,3266,3344,3450,3556,3635,3715,4021,4079,4955,5030,5095,5161,5221,5282,5354,5427,5494,5562,5621,5680,5739,5798,5857,5911,5965,6018,6072,6126,6180,6234,6598,6677,6750,6824,6895,6967,7039,7112,7169,7227,7300,7374,7448,7523,7595,7668,7738,7809,7869,7930,7999,8068,8138,8212,8288,8352,8429,8505,8582,8647,8716,8793,8868,8937,9005,9082,9148,9209,9306,9371,9440,9539,9610,9669,9727,9784,9843,9907,9978,10050,10122,10194,10266,10333,10401,10469,10528,10591,10655,10745,10836,10896,10962,11029,11095,11165,11229,11282,11349,11410,11477,11590,11648,11711,11776,11841,11916,11989,12061,12105,12152,12198,12247,12308,12369,12430,12492,12556,12620,12684,12749,12812,12872,12933,12999,13058,13118,13180,13251,13311,13379,14096,14183,14436,14523,14611,14693,14776,14866,14957,16855,16913,16958,17024,17088,17145,17202,17256,19436,19484,19533,19584,19822,20205,20254,20509,21666,22001,22063,22123,22180,22518,22588,22666,22720,22790,22875,22923,22969,23030,23093,23159,23223,23294,23357,23422,23486,23547,23608,23660,23733,23807,23876,23951,24025,24099,24240,24310,28003,28389,28479,28567,28663,28753,29335,29424,29671,29952,30204,30489,30882,31359,31581,31803,32079,32306,32536,32766,32996,33226,33453,33872,34098,34523,34753,35181,35400,35683,35891,36022,36249,36675,36900,37327,37548,37973,38093,38369,38670,38994,39285,39599,39736,39867,39972,40214,40381,40585,40793,41064,41176,41288,41393,41510,41724,41870,42010,42096,42444,42532,42778,43196,43445,43527,43625,44282,44382,44634,45058,45313,45407,45496,45733,47757,47999,48101,48354,50510,61191,62707,73402,74930,76687,77313,77733,78994,80259,80515,80751,81298,81792,82397,82595,83175,84543,84918,85036,85574,85731,85927,86200,86456,86626,86767,86831,87196,87563,88239,88503,88841,89194,89288,89474,89780,90042,90167,90294,90533,90744,90863,91056,91233,91688,91869,91991,92250,92363,92550,92652,92759,92888,93163,93671,94167,95044,95338,95908,96057,96789,96961,97045,97381,97473,97751,105041,110412,110474,111052,111636,111727,119696,119925,120085,120237,120408,120574,120743,120910,121073,121316,121486,121659,121830,122104,122303,122508,122838,122922,123018,123114,123212,123312,123414,123516,123618,123720,123822,123922,124018,124130,124259,124382,124513,124644,124742,124856,124950,125090,125224,125320,125432,125532,125648,125744,125856,125956,126096,126232,126396,126526,126684,126834,126975,127119,127254,127366,127516,127644,127772,127908,128040,128170,128300,128412,128552,129838,129982,130120,130186,130276,130352,130456,130546,130648,130756,130864,130964,131044,131136,131234,131344,131396,131474,131580,131672,131776,131886,132008,132171,132328,133052,133152,133242,133352,133442,133683,133777,133883,133975,134075,134187,134301,134417,134533,134627,134741,134853,134955,135075,135197,135279,135383,135503,135629,135727,135821,135909,136021,136137,136259,136371,136546,136662,136748,136840,136952,137076,137143,137269,137337,137465,137609,137737,137806,137901,138016,138129,138228,138337,138448,138559,138660,138765,138865,138995,139086,139209,139303,139415,139501,139605,139701,139789,139907,140011,140115,140241,140329,140437,140537,140627,140737,140821,140923,141007,141061,141125,141231,141317,141427,141511,141631,144531,144649,144764,144844,145205,145438,146308,147273,148617,149978,150366,153209,163262,163582,165556,167214,173242,177722,177984,178184,179210,183488,184094,184571,184722,189394,191272,192453,197567,199409,201540,201880,203191,203394"}}, {"source": "C:\\Users\\<USER>\\Desktop\\pfe\\mobile\\ecocode-mobile-version-ecocode-final\\example\\myApp\\android\\app\\src\\staging\\res\\values\\styles.xml", "from": {"startLines": "3,18", "startColumns": "4,4", "startOffsets": "173,1089", "endLines": "11,20", "endColumns": "12,12", "endOffsets": "747,1253"}, "to": {"startLines": "1551,1559", "startColumns": "4,4", "startOffsets": "99194,99646", "endLines": "1558,1561", "endColumns": "12,12", "endOffsets": "99641,99810"}}, {"source": "C:\\Users\\<USER>\\Desktop\\pfe\\mobile\\ecocode-mobile-version-ecocode-final\\example\\myApp\\build\\app\\generated\\res\\resValues\\staging\\debug\\values\\gradleResValues.xml", "from": {"startLines": "6", "startColumns": "4", "startOffsets": "159", "endColumns": "68", "endOffsets": "223"}, "to": {"startLines": "404", "startColumns": "4", "startOffsets": "24398", "endColumns": "68", "endOffsets": "24462"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8d129809cd8c466c0da9d5b65b255a5e\\transformed\\jetified-savedstate-1.2.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "53", "endOffsets": "104"}, "to": {"startLines": "365", "startColumns": "4", "startOffsets": "21774", "endColumns": "53", "endOffsets": "21823"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d7cf2d8404d700a18769a5d26d9a8c78\\transformed\\jetified-startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "403", "startColumns": "4", "startOffsets": "24315", "endColumns": "82", "endOffsets": "24393"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ac0dcf16ce6a281444fe134795f48771\\transformed\\jetified-firebase-messaging-24.1.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "81", "endOffsets": "132"}, "to": {"startLines": "436", "startColumns": "4", "startOffsets": "27689", "endColumns": "81", "endOffsets": "27766"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\a467c1fd402840055b9892cad66e8764\\transformed\\transition-1.4.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,95,142,185,240,287,341,393,442,503", "endColumns": "39,46,42,54,46,53,51,48,60,49", "endOffsets": "90,137,180,235,282,336,388,437,498,548"}, "to": {"startLines": "324,325,330,337,338,357,358,359,360,361", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "19706,19746,19963,20301,20356,21373,21427,21479,21528,21589", "endColumns": "39,46,42,54,46,53,51,48,60,49", "endOffsets": "19741,19788,20001,20351,20398,21422,21474,21523,21584,21634"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\549f9b90956411ff250116223d81245e\\transformed\\jetified-activity-1.8.1\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,97", "endColumns": "41,59", "endOffsets": "92,152"}, "to": {"startLines": "336,364", "startColumns": "4,4", "startOffsets": "20259,21714", "endColumns": "41,59", "endOffsets": "20296,21769"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\1c916160872209709351e900651600df\\transformed\\lifecycle-runtime-2.7.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "363", "startColumns": "4", "startOffsets": "21671", "endColumns": "42", "endOffsets": "21709"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\e29af6bba5b83fbf9d8cabbd1c723f32\\transformed\\jetified-appauth-0.11.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endLines": "11", "endColumns": "12", "endOffsets": "694"}, "to": {"startLines": "2036", "startColumns": "4", "startOffsets": "132333", "endLines": "2045", "endColumns": "12", "endOffsets": "132972"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\7be7d9406e844f92d0315bd988141b00\\transformed\\browser-1.8.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,113,179,242,304,375,447,515,582,661", "endColumns": "57,65,62,61,70,71,67,66,78,68", "endOffsets": "108,174,237,299,370,442,510,577,656,725"}, "to": {"startLines": "82,83,84,85,225,226,431,433,434,435", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "3720,3778,3844,3907,13384,13455,27349,27474,27541,27620", "endColumns": "57,65,62,61,70,71,67,66,78,68", "endOffsets": "3773,3839,3902,3964,13450,13522,27412,27536,27615,27684"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\5b162dcac78a2ae956fb790147e5268a\\transformed\\recyclerview-1.0.0\\res\\values\\values.xml", "from": {"startLines": "30,31,32,33,34,35,36,2", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "1535,1594,1642,1698,1773,1849,1921,55", "endLines": "30,31,32,33,34,35,36,29", "endColumns": "58,47,55,74,75,71,65,24", "endOffsets": "1589,1637,1693,1768,1844,1916,1982,1530"}, "to": {"startLines": "236,237,238,246,247,248,327,3524", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "14188,14247,14295,14962,15037,15113,19827,189399", "endLines": "236,237,238,246,247,248,327,3543", "endColumns": "58,47,55,74,75,71,65,24", "endOffsets": "14242,14290,14346,15032,15108,15180,19888,190189"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\4a1a08cc053a416e875e44f0df90f81c\\transformed\\fragment-1.7.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,10", "startColumns": "4,4,4,4,4", "startOffsets": "55,112,177,241,411", "endLines": "2,3,4,9,13", "endColumns": "56,64,63,24,24", "endOffsets": "107,172,236,406,555"}, "to": {"startLines": "323,339,367,3088,3093", "startColumns": "4,4,4,4,4", "startOffsets": "19649,20403,21878,175808,175978", "endLines": "323,339,367,3092,3096", "endColumns": "56,64,63,24,24", "endOffsets": "19701,20463,21937,175973,176122"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\1d4beccfb936038cb8ed9ff0bd118a86\\transformed\\coordinatorlayout-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,102,3,13", "startColumns": "4,4,4,4", "startOffsets": "55,5935,116,724", "endLines": "2,104,12,101", "endColumns": "60,12,24,24", "endOffsets": "111,6075,719,5930"}, "to": {"startLines": "3,2155,2902,2908", "startColumns": "4,4,4,4", "startOffsets": "164,141775,167219,167430", "endLines": "3,2157,2907,2991", "endColumns": "60,12,24,24", "endOffsets": "220,141915,167425,171941"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\dad65cb79b3eafd50888ad50598d546f\\transformed\\jetified-window-1.2.0\\res\\values\\values.xml", "from": {"startLines": "2,3,9,17,25,37,43,49,50,51,52,53,54,55,61,66,74,89", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,114,287,506,725,1039,1227,1414,1467,1527,1579,1624,1663,1723,1918,2076,2358,2972", "endLines": "2,8,16,24,36,42,48,49,50,51,52,53,54,60,65,73,88,104", "endColumns": "58,11,11,11,11,11,11,52,59,51,44,38,59,24,24,24,24,24", "endOffsets": "109,282,501,720,1034,1222,1409,1462,1522,1574,1619,1658,1718,1913,2071,2353,2967,3621"}, "to": {"startLines": "2,5,11,19,30,42,48,54,55,56,57,58,322,2262,2268,3623,3631,3646", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,280,453,672,1045,1359,1547,1734,1787,1847,1899,1944,19589,145443,145638,192458,192740,193354", "endLines": "2,10,18,26,41,47,53,54,55,56,57,58,322,2267,2272,3630,3645,3661", "endColumns": "58,11,11,11,11,11,11,52,59,51,44,38,59,24,24,24,24,24", "endOffsets": "159,448,667,886,1354,1542,1729,1782,1842,1894,1939,1978,19644,145633,145791,192735,193349,194003"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b706e9705966f9637699d6002c5f2d79\\transformed\\lifecycle-viewmodel-2.7.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "366", "startColumns": "4", "startOffsets": "21828", "endColumns": "49", "endOffsets": "21873"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b47f2b5b543d4723cc676f4f1e95e1d1\\transformed\\preference-1.2.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,22,23,24,25,42,45,51,57,60,66,70,73,80,86,89,95,100,105,112,114,120,126,134,139,146,151,157,161,168,172,178,184,187,192,193,194,199,215,238,243,257,268,348,358,368,386,392,439,461,485", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,178,247,311,366,434,501,566,623,680,728,776,837,900,963,1001,1058,1102,1242,1381,1431,1479,2917,3022,3378,3716,3862,4202,4414,4577,4984,5322,5445,5784,6023,6280,6651,6711,7049,7335,7784,8076,8464,8769,9113,9358,9688,9895,10163,10436,10580,10949,10996,11052,11308,12367,13788,14126,15012,15622,20168,20687,21229,22503,22763,25467,26989,28470", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,21,22,23,24,41,44,50,56,59,65,69,72,79,85,88,94,99,104,111,113,119,125,133,138,145,150,156,160,167,171,177,183,186,191,192,193,198,214,237,242,256,267,347,357,367,385,391,438,460,484,508", "endColumns": "72,68,63,54,67,66,64,56,56,47,47,60,62,62,37,56,43,13,138,49,47,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,46,55,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "173,242,306,361,429,496,561,618,675,723,771,832,895,958,996,1053,1097,1237,1376,1426,1474,2912,3017,3373,3711,3857,4197,4409,4572,4979,5317,5440,5779,6018,6275,6646,6706,7044,7330,7779,8071,8459,8764,9108,9353,9683,9890,10158,10431,10575,10944,10991,11047,11303,12362,13783,14121,15007,15617,20163,20682,21224,22498,22758,25462,26984,28465,29984"}, "to": {"startLines": "62,123,264,265,266,267,268,269,270,331,332,333,373,374,430,432,437,438,443,444,445,1534,1722,1725,1731,1737,1740,1746,1750,1753,1760,1766,1769,1775,1780,1785,1792,1794,1800,1806,1814,1819,1826,1831,1837,1841,1848,1852,1858,1864,1867,1871,1872,2810,2825,2992,3030,3172,3360,3378,3442,3452,3462,3469,3475,3579,3748,3765", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2141,6460,16163,16227,16282,16350,16417,16482,16539,20006,20054,20102,22253,22316,27311,27417,27771,27815,28079,28218,28268,97756,111732,111837,112082,112420,112566,112906,113118,113281,113688,114026,114149,114488,114727,114984,115355,115415,115753,116039,116488,116780,117168,117473,117817,118062,118392,118599,118867,119140,119284,119485,119532,163267,163790,171946,173247,178189,184099,184727,186652,186934,187239,187501,187761,191277,197572,198102", "endLines": "62,123,264,265,266,267,268,269,270,331,332,333,373,374,430,432,437,440,443,444,445,1550,1724,1730,1736,1739,1745,1749,1752,1759,1765,1768,1774,1779,1784,1791,1793,1799,1805,1813,1818,1825,1830,1836,1840,1847,1851,1857,1863,1866,1870,1871,1872,2814,2835,3011,3033,3181,3367,3441,3451,3461,3468,3474,3517,3591,3764,3781", "endColumns": "72,68,63,54,67,66,64,56,56,47,47,60,62,62,37,56,43,13,138,49,47,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,46,55,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "2209,6524,16222,16277,16345,16412,16477,16534,16591,20049,20097,20158,22311,22374,27344,27469,27810,27950,28213,28263,28311,99189,111832,112077,112415,112561,112901,113113,113276,113683,114021,114144,114483,114722,114979,115350,115410,115748,116034,116483,116775,117163,117468,117812,118057,118387,118594,118862,119135,119279,119480,119527,119583,163447,164186,172670,173391,178516,184342,186647,186929,187234,187496,187756,189179,191724,198097,198665"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\929ae1d3a00f8bfd8a9a1aa81922ea47\\transformed\\media-1.1.0\\res\\values\\values.xml", "from": {"startLines": "2,5,6,7,8,10,11,12,13,14", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,288,341,394,447,560,626,748,809,875", "endColumns": "88,52,52,52,52,65,121,60,65,66", "endOffsets": "139,336,389,442,495,621,743,804,870,937"}, "to": {"startLines": "122,271,272,273,274,1989,1991,1992,1997,1999", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "6371,16596,16649,16702,16755,128875,129051,129173,129435,129630", "endColumns": "88,52,52,52,52,65,121,60,65,66", "endOffsets": "6455,16644,16697,16750,16803,128936,129168,129229,129496,129692"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\6b81f176e99a87272c49b1d24ec2636b\\transformed\\jetified-appcompat-resources-1.6.1\\res\\values\\values.xml", "from": {"startLines": "2,18,24,34,50", "startColumns": "4,4,4,4,4", "startOffsets": "55,480,658,942,1353", "endLines": "17,23,33,49,53", "endColumns": "24,24,24,24,24", "endOffsets": "475,653,937,1348,1475"}, "to": {"startLines": "2283,2299,2305,3662,3678", "startColumns": "4,4,4,4,4", "startOffsets": "146313,146738,146916,194008,194419", "endLines": "2298,2304,2314,3677,3681", "endColumns": "24,24,24,24,24", "endOffsets": "146733,146911,147195,194414,194541"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8688e9838b51b6004ba54c59160ddf37\\transformed\\jetified-play-services-base-18.1.0\\res\\values\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,33,46", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "215,301,377,463,549,625,702,778,951,1052,1233,1354,1457,1637,1756,1868,1967,2155,2256,2437,2558,2733,2877,2936,2994,3164,3475", "endLines": "4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,45,64", "endColumns": "85,75,85,85,75,76,75,75,100,180,120,102,179,118,111,98,187,100,180,120,174,143,58,57,74,20,20", "endOffsets": "300,376,462,548,624,701,777,853,1051,1232,1353,1456,1636,1755,1867,1966,2154,2255,2436,2557,2732,2876,2935,2993,3068,3474,3887"}, "to": {"startLines": "90,91,92,93,94,95,96,97,412,413,414,415,416,417,418,419,421,422,423,424,425,426,427,428,429,3182,3592", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4215,4305,4385,4475,4565,4645,4726,4806,24978,25083,25264,25389,25496,25676,25799,25915,26185,26373,26478,26659,26784,26959,27107,27170,27232,178521,191729", "endLines": "90,91,92,93,94,95,96,97,412,413,414,415,416,417,418,419,421,422,423,424,425,426,427,428,429,3194,3610", "endColumns": "89,79,89,89,79,80,79,79,104,180,124,106,179,122,115,102,187,104,180,124,174,147,62,61,78,20,20", "endOffsets": "4300,4380,4470,4560,4640,4721,4801,4881,25078,25259,25384,25491,25671,25794,25910,26013,26368,26473,26654,26779,26954,27102,27165,27227,27306,178831,192141"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\1f8ae2ae03c8f3ae6e8dc60fe5ef01da\\transformed\\jetified-play-services-basement-18.3.0\\res\\values\\values.xml", "from": {"startLines": "4,7", "startColumns": "0,0", "startOffsets": "243,406", "endColumns": "63,166", "endOffsets": "306,572"}, "to": {"startLines": "372,420", "startColumns": "4,4", "startOffsets": "22185,26018", "endColumns": "67,166", "endOffsets": "22248,26180"}}]}]}