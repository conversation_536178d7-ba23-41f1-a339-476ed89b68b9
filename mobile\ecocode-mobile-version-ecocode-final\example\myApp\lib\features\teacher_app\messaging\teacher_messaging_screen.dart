import 'package:flutter/material.dart';
import 'package:NovaSchool/features/teacher_app/Home/T_appDrawer.dart';
import 'package:NovaSchool/services/auth_service.dart';
import 'package:NovaSchool/services/message_services.dart';
import 'package:NovaSchool/features/teacher_app/messaging/teacher_conversation_screen.dart';
import 'package:NovaSchool/features/teacher_app/messaging/teacher_compose_message_screen.dart';

class TMessagingScreen extends StatefulWidget {
  const TMessagingScreen({Key? key}) : super(key: key);

  @override
  _TMessagingScreenState createState() => _TMessagingScreenState();
}

class _TMessagingScreenState extends State<TMessagingScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final MessageServices _messageService = MessageServices();
  
  List<ConversationItem> _conversations = [];
  List<ContactItem> _contacts = [];
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _loadData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    setState(() => _isLoading = true);
    try {
      // TODO: Load actual conversations and contacts from API
      _loadMockData();
    } catch (e) {
      _showErrorDialog('Erreur lors du chargement: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  void _loadMockData() {
    // Mock data for demonstration
    _conversations = [
      ConversationItem(
        id: '1',
        participantName: 'Administration',
        participantType: 'Admin',
        lastMessage: 'Merci pour votre rapport sur les absences.',
        timestamp: '14:30',
        unreadCount: 1,
        avatar: 'AD',
      ),
      ConversationItem(
        id: '2',
        participantName: 'Marie Dupont',
        participantType: 'Parent',
        lastMessage: 'Pouvez-vous me donner des nouvelles de Pierre ?',
        timestamp: 'Hier',
        unreadCount: 0,
        avatar: 'MD',
      ),
      ConversationItem(
        id: '3',
        participantName: 'Jean Martin',
        participantType: 'Parent',
        lastMessage: 'Merci pour les devoirs supplémentaires.',
        timestamp: '15/01',
        unreadCount: 2,
        avatar: 'JM',
      ),
    ];

    _contacts = [
      ContactItem(
        id: '1',
        name: 'Administration',
        type: 'Admin',
        subject: 'Direction de l\'école',
        avatar: 'AD',
      ),
      ContactItem(
        id: '2',
        name: 'Marie Dupont',
        type: 'Parent',
        subject: 'Parent de Pierre Dupont',
        avatar: 'MD',
      ),
      ContactItem(
        id: '3',
        name: 'Jean Martin',
        type: 'Parent',
        subject: 'Parent de Sophie Martin',
        avatar: 'JM',
      ),
      ContactItem(
        id: '4',
        name: 'Sarah Bernard',
        type: 'Parent',
        subject: 'Parent de Lucas Bernard',
        avatar: 'SB',
      ),
    ];
  }

  void _showErrorDialog(String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Erreur'),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  void _navigateToConversation(ConversationItem conversation) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => TConversationScreen(conversation: conversation),
      ),
    );
  }

  void _navigateToCompose() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => TComposeMessageScreen(contacts: _contacts),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF2F2F2),
      drawer: TeacherDrawer(),
      appBar: PreferredSize(
        preferredSize: const Size.fromHeight(kToolbarHeight),
        child: Container(
          width: double.infinity,
          decoration: const BoxDecoration(
            gradient: LinearGradient(colors: [Colors.blue, Colors.cyan]),
          ),
          child: AppBar(
            backgroundColor: Colors.transparent,
            elevation: 0,
            centerTitle: true,
            iconTheme: const IconThemeData(
              color: Colors.white,
              size: 30,
            ),
            title: const Text(
              'Messagerie',
              style: TextStyle(
                color: Colors.white,
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            actions: [
              IconButton(
                icon: const Icon(Icons.logout, color: Colors.white),
                onPressed: () async {
                  await AuthService.logoutKeycloak();
                },
              ),
            ],
            bottom: TabBar(
              controller: _tabController,
              labelColor: Colors.white,
              unselectedLabelColor: Colors.white70,
              indicatorColor: Colors.white,
              tabs: const [
                Tab(text: 'Conversations'),
                Tab(text: 'Contacts'),
              ],
            ),
          ),
        ),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : TabBarView(
              controller: _tabController,
              children: [
                _buildConversationsTab(),
                _buildContactsTab(),
              ],
            ),
      floatingActionButton: FloatingActionButton(
        onPressed: _navigateToCompose,
        backgroundColor: Colors.blue,
        child: const Icon(Icons.edit, color: Colors.white),
      ),
    );
  }

  Widget _buildConversationsTab() {
    if (_conversations.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.chat_bubble_outline, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text(
              'Aucune conversation',
              style: TextStyle(fontSize: 16, color: Colors.grey),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(8),
      itemCount: _conversations.length,
      itemBuilder: (context, index) {
        final conversation = _conversations[index];
        return Card(
          margin: const EdgeInsets.symmetric(vertical: 4),
          child: ListTile(
            leading: CircleAvatar(
              backgroundColor: conversation.participantType == 'Admin' 
                  ? Colors.red 
                  : Colors.orange,
              child: Text(
                conversation.avatar,
                style: const TextStyle(color: Colors.white, fontSize: 12),
              ),
            ),
            title: Row(
              children: [
                Expanded(
                  child: Text(
                    conversation.participantName,
                    style: const TextStyle(fontWeight: FontWeight.w600),
                  ),
                ),
                Text(
                  conversation.timestamp,
                  style: const TextStyle(fontSize: 12, color: Colors.grey),
                ),
              ],
            ),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  conversation.participantType,
                  style: TextStyle(
                    fontSize: 12,
                    color: conversation.participantType == 'Admin' 
                        ? Colors.red 
                        : Colors.orange,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  conversation.lastMessage,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
            trailing: conversation.unreadCount > 0
                ? CircleAvatar(
                    radius: 10,
                    backgroundColor: Colors.red,
                    child: Text(
                      '${conversation.unreadCount}',
                      style: const TextStyle(color: Colors.white, fontSize: 10),
                    ),
                  )
                : null,
            onTap: () => _navigateToConversation(conversation),
          ),
        );
      },
    );
  }

  Widget _buildContactsTab() {
    if (_contacts.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.contacts, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text(
              'Aucun contact',
              style: TextStyle(fontSize: 16, color: Colors.grey),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(8),
      itemCount: _contacts.length,
      itemBuilder: (context, index) {
        final contact = _contacts[index];
        return Card(
          margin: const EdgeInsets.symmetric(vertical: 4),
          child: ListTile(
            leading: CircleAvatar(
              backgroundColor: contact.type == 'Admin' ? Colors.red : Colors.orange,
              child: Text(
                contact.avatar,
                style: const TextStyle(color: Colors.white, fontSize: 12),
              ),
            ),
            title: Text(
              contact.name,
              style: const TextStyle(fontWeight: FontWeight.w600),
            ),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  contact.type,
                  style: TextStyle(
                    fontSize: 12,
                    color: contact.type == 'Admin' ? Colors.red : Colors.orange,
                  ),
                ),
                Text(contact.subject),
              ],
            ),
            trailing: IconButton(
              icon: const Icon(Icons.message, color: Colors.blue),
              onPressed: () {
                // TODO: Start conversation with this contact
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(content: Text('Démarrer conversation avec ${contact.name}')),
                );
              },
            ),
          ),
        );
      },
    );
  }
}

// Data models
class ConversationItem {
  final String id;
  final String participantName;
  final String participantType;
  final String lastMessage;
  final String timestamp;
  final int unreadCount;
  final String avatar;

  ConversationItem({
    required this.id,
    required this.participantName,
    required this.participantType,
    required this.lastMessage,
    required this.timestamp,
    required this.unreadCount,
    required this.avatar,
  });
}

class ContactItem {
  final String id;
  final String name;
  final String type;
  final String subject;
  final String avatar;

  ContactItem({
    required this.id,
    required this.name,
    required this.type,
    required this.subject,
    required this.avatar,
  });
}
