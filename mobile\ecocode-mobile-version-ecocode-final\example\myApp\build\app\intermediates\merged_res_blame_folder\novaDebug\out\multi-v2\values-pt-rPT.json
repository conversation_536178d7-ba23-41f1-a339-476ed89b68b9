{"logs": [{"outputFile": "com.example.NovaSchool.app-mergeNovaDebugResources-52:/values-pt-rPT/values-pt-rPT.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\57c1c0bacecfc631670f89f9a212e268\\transformed\\appcompat-1.6.1\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,319,426,515,616,734,819,899,991,1085,1182,1276,1375,1469,1565,1660,1752,1844,1929,2036,2147,2249,2357,2465,2572,2737,2836", "endColumns": "107,105,106,88,100,117,84,79,91,93,96,93,98,93,95,94,91,91,84,106,110,101,107,107,106,164,98,85", "endOffsets": "208,314,421,510,611,729,814,894,986,1080,1177,1271,1370,1464,1560,1655,1747,1839,1924,2031,2142,2244,2352,2460,2567,2732,2831,2917"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,62", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,319,426,515,616,734,819,899,991,1085,1182,1276,1375,1469,1565,1660,1752,1844,1929,2036,2147,2249,2357,2465,2572,2737,6720", "endColumns": "107,105,106,88,100,117,84,79,91,93,96,93,98,93,95,94,91,91,84,106,110,101,107,107,106,164,98,85", "endOffsets": "208,314,421,510,611,729,814,894,986,1080,1177,1271,1370,1464,1560,1655,1747,1839,1924,2031,2142,2244,2352,2460,2567,2732,2831,6801"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\42462ff615a470fce269a489eb930f99\\transformed\\core-1.13.1\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,353,453,560,666,787", "endColumns": "96,101,98,99,106,105,120,100", "endOffsets": "147,249,348,448,555,661,782,883"}, "to": {"startLines": "29,30,31,32,33,34,35,63", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2836,2933,3035,3134,3234,3341,3447,6806", "endColumns": "96,101,98,99,106,105,120,100", "endOffsets": "2928,3030,3129,3229,3336,3442,3563,6902"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\1f8ae2ae03c8f3ae6e8dc60fe5ef01da\\transformed\\jetified-play-services-basement-18.3.0\\res\\values-pt-rPT\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "199", "endColumns": "140", "endOffsets": "339"}, "to": {"startLines": "44", "startColumns": "4", "startOffsets": "4599", "endColumns": "144", "endOffsets": "4739"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b47f2b5b543d4723cc676f4f1e95e1d1\\transformed\\preference-1.2.1\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,175,263,342,493,662,749", "endColumns": "69,87,78,150,168,86,80", "endOffsets": "170,258,337,488,657,744,825"}, "to": {"startLines": "54,56,60,61,64,65,66", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "5902,6088,6490,6569,6907,7076,7163", "endColumns": "69,87,78,150,168,86,80", "endOffsets": "5967,6171,6564,6715,7071,7158,7239"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\7be7d9406e844f92d0315bd988141b00\\transformed\\browser-1.8.0\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,171,270,382", "endColumns": "115,98,111,102", "endOffsets": "166,265,377,480"}, "to": {"startLines": "55,57,58,59", "startColumns": "4,4,4,4", "startOffsets": "5972,6176,6275,6387", "endColumns": "115,98,111,102", "endOffsets": "6083,6270,6382,6485"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8688e9838b51b6004ba54c59160ddf37\\transformed\\jetified-play-services-base-18.1.0\\res\\values-pt-rPT\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "197,298,457,581,685,849,973,1091,1196,1380,1484,1650,1777,1932,2106,2170,2235", "endColumns": "100,158,123,103,163,123,117,104,183,103,165,126,154,173,63,64,82", "endOffsets": "297,456,580,684,848,972,1090,1195,1379,1483,1649,1776,1931,2105,2169,2234,2317"}, "to": {"startLines": "36,37,38,39,40,41,42,43,45,46,47,48,49,50,51,52,53", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3568,3673,3836,3964,4072,4240,4368,4490,4744,4932,5040,5210,5341,5500,5678,5746,5815", "endColumns": "104,162,127,107,167,127,121,108,187,107,169,130,158,177,67,68,86", "endOffsets": "3668,3831,3959,4067,4235,4363,4485,4594,4927,5035,5205,5336,5495,5673,5741,5810,5897"}}]}]}