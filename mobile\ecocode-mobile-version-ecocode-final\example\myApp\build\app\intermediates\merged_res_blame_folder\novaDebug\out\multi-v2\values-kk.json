{"logs": [{"outputFile": "com.example.NovaSchool.app-mergeNovaDebugResources-52:/values-kk/values-kk.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\1f8ae2ae03c8f3ae6e8dc60fe5ef01da\\transformed\\jetified-play-services-basement-18.3.0\\res\\values-kk\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "159", "endOffsets": "354"}, "to": {"startLines": "44", "startColumns": "4", "startOffsets": "4496", "endColumns": "163", "endOffsets": "4655"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\7be7d9406e844f92d0315bd988141b00\\transformed\\browser-1.8.0\\res\\values-kk\\values-kk.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,155,259,367", "endColumns": "99,103,107,104", "endOffsets": "150,254,362,467"}, "to": {"startLines": "55,57,58,59", "startColumns": "4,4,4,4", "startOffsets": "5797,5983,6087,6195", "endColumns": "99,103,107,104", "endOffsets": "5892,6082,6190,6295"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8688e9838b51b6004ba54c59160ddf37\\transformed\\jetified-play-services-base-18.1.0\\res\\values-kk\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,292,441,563,665,801,923,1042,1147,1308,1410,1563,1688,1837,1990,2049,2104", "endColumns": "98,148,121,101,135,121,118,104,160,101,152,124,148,152,58,54,73", "endOffsets": "291,440,562,664,800,922,1041,1146,1307,1409,1562,1687,1836,1989,2048,2103,2177"}, "to": {"startLines": "36,37,38,39,40,41,42,43,45,46,47,48,49,50,51,52,53", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3510,3613,3766,3892,3998,4138,4264,4387,4660,4825,4931,5088,5217,5370,5527,5590,5649", "endColumns": "102,152,125,105,139,125,122,108,164,105,156,128,152,156,62,58,77", "endOffsets": "3608,3761,3887,3993,4133,4259,4382,4491,4820,4926,5083,5212,5365,5522,5585,5644,5722"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\57c1c0bacecfc631670f89f9a212e268\\transformed\\appcompat-1.6.1\\res\\values-kk\\values-kk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,318,428,513,619,738,818,895,986,1079,1174,1268,1368,1461,1556,1653,1744,1835,1916,2021,2124,2222,2329,2435,2535,2701,2796", "endColumns": "107,104,109,84,105,118,79,76,90,92,94,93,99,92,94,96,90,90,80,104,102,97,106,105,99,165,94,81", "endOffsets": "208,313,423,508,614,733,813,890,981,1074,1169,1263,1363,1456,1551,1648,1739,1830,1911,2016,2119,2217,2324,2430,2530,2696,2791,2873"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,62", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,318,428,513,619,738,818,895,986,1079,1174,1268,1368,1461,1556,1653,1744,1835,1916,2021,2124,2222,2329,2435,2535,2701,6522", "endColumns": "107,104,109,84,105,118,79,76,90,92,94,93,99,92,94,96,90,90,80,104,102,97,106,105,99,165,94,81", "endOffsets": "208,313,423,508,614,733,813,890,981,1074,1169,1263,1363,1456,1551,1648,1739,1830,1911,2016,2119,2217,2324,2430,2530,2696,2791,6599"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b47f2b5b543d4723cc676f4f1e95e1d1\\transformed\\preference-1.2.1\\res\\values-kk\\values-kk.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,175,261,340,483,652,736", "endColumns": "69,85,78,142,168,83,79", "endOffsets": "170,256,335,478,647,731,811"}, "to": {"startLines": "54,56,60,61,64,65,66", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "5727,5897,6300,6379,6705,6874,6958", "endColumns": "69,85,78,142,168,83,79", "endOffsets": "5792,5978,6374,6517,6869,6953,7033"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\42462ff615a470fce269a489eb930f99\\transformed\\core-1.13.1\\res\\values-kk\\values-kk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,252,354,457,561,658,769", "endColumns": "94,101,101,102,103,96,110,100", "endOffsets": "145,247,349,452,556,653,764,865"}, "to": {"startLines": "29,30,31,32,33,34,35,63", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2796,2891,2993,3095,3198,3302,3399,6604", "endColumns": "94,101,101,102,103,96,110,100", "endOffsets": "2886,2988,3090,3193,3297,3394,3505,6700"}}]}]}