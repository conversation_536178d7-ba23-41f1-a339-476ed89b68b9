{"logs": [{"outputFile": "com.example.NovaSchool.app-mergeTacapesDebugResources-51:/values-night-v8/values-night-v8.xml", "map": [{"source": "C:\\Users\\<USER>\\Desktop\\pfe\\mobile\\ecocode-mobile-version-ecocode-final\\example\\myApp\\android\\app\\src\\tacapes\\res\\values-night\\styles.xml", "from": {"startLines": "3,18", "startColumns": "4,4", "startOffsets": "172,1088", "endLines": "11,20", "endColumns": "12,12", "endOffsets": "746,1252"}, "to": {"startLines": "2,10", "startColumns": "4,4", "startOffsets": "55,507", "endLines": "9,12", "endColumns": "12,12", "endOffsets": "502,671"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\57c1c0bacecfc631670f89f9a212e268\\transformed\\appcompat-1.6.1\\res\\values-night-v8\\values-night-v8.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,125,209,293,389,491,593,687", "endColumns": "69,83,83,95,101,101,93,88", "endOffsets": "120,204,288,384,486,588,682,771"}, "to": {"startLines": "13,14,15,16,17,18,19,20", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "676,746,830,914,1010,1112,1214,1308", "endColumns": "69,83,83,95,101,101,93,88", "endOffsets": "741,825,909,1005,1107,1209,1303,1392"}}]}]}